[{"text": "📖 Overview", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AI-assisted Functional Testing", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28AI-assisted%20Functional%20Testing.one%7Cdcf8b742-ce39-460d-98f6-a79e95a4f0f0%2F%F0%9F%93%96%20Overview%7Ca9f886b7-8c13-6348-a970-719b5561864f%2F%29"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AI-assisted Functional Testing", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28AI-assisted%20Functional%20Testing.one%7Cdcf8b742-ce39-460d-98f6-a79e95a4f0f0%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cb0f8a987-251f-2044-a4bf-af4d053bb350%2F%29"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AI-assisted Functional Testing", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28AI-assisted%20Functional%20Testing.one%7Cdcf8b742-ce39-460d-98f6-a79e95a4f0f0%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C84b80404-6dbb-7448-84af-ff2741ad68fb%2F%29"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AI-assisted Functional Testing", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28AI-assisted%20Functional%20Testing.one%7Cdcf8b742-ce39-460d-98f6-a79e95a4f0f0%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C3577cf6b-393d-7641-8afc-68edddc77c58%2F%29"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AI-assisted Functional Testing", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28AI-assisted%20Functional%20Testing.one%7Cdcf8b742-ce39-460d-98f6-a79e95a4f0f0%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C7f2d36a7-d019-0640-ae81-215f7ba7608e%2F%29"}}, {"text": "Test Sigma\n\n\n\n\n\n\nWhile navigating, Co-pilot focuses on the current page, which helps in contextual relevance for current.\nTo start with recorder option first step should be created with \"url\".\nIn terms of screenshot coverage, it is acceptable to state that Co-pilot provides good visual coverage\nCo-pilot provides a generic test case, even when it is focusing on the current page. It should ideally generate a ready-to-run test case.\nEditing test steps is still easy.\nNot able to create single test case in using Co-pilot as its create multiple and some are irrelevant.\nCo-pilot does not seem to understand prompts correctly.\nWeak prompt comprehension.\nLacks agentic workflow for streamlined test case creation.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AI-assisted Functional Testing", "page": "Test Sigma", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28AI-assisted%20Functional%20Testing.one%7Cdcf8b742-ce39-460d-98f6-a79e95a4f0f0%2FTest%20Sigma%7C08a02608-7684-4e42-bc66-dfcaf4873b8d%2F%29"}}, {"text": "Strategy", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AI-assisted Functional Testing", "page": "Strategy", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28AI-assisted%20Functional%20Testing.one%7Cdcf8b742-ce39-460d-98f6-a79e95a4f0f0%2FStrategy%7C09efd43c-5dd6-42f0-926f-94ac455f9081%2F%29"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AWS lambda docker brickwork with next.js", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28AWS%20lambda%20docker%20brickwork%20with%20next.js.one%7C4027bbdf-a256-4bcc-afc5-fbbda6343a70%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C5f02876d-69ff-4268-89f7-43f399f9ef8a%2F%29"}}, {"text": "📖 Overview", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AWS lambda docker brickwork with next.js", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28AWS%20lambda%20docker%20brickwork%20with%20next.js.one%7C4027bbdf-a256-4bcc-afc5-fbbda6343a70%2F%F0%9F%93%96%20Overview%7Ceb0056ef-b30f-4887-9e0f-72ce6e90637e%2F%29"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AWS lambda docker brickwork with next.js", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28AWS%20lambda%20docker%20brickwork%20with%20next.js.one%7C4027bbdf-a256-4bcc-afc5-fbbda6343a70%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C57e317a8-9d40-46ea-a34d-e6a626c370a0%2F%29"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AWS lambda docker brickwork with next.js", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28AWS%20lambda%20docker%20brickwork%20with%20next.js.one%7C4027bbdf-a256-4bcc-afc5-fbbda6343a70%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C67a286ce-0bdf-4fff-a364-5984e2c1f46a%2F%29"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AWS lambda docker brickwork with next.js", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28AWS%20lambda%20docker%20brickwork%20with%20next.js.one%7C4027bbdf-a256-4bcc-afc5-fbbda6343a70%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cc9dadf20-83f8-467a-a7ab-e098531e9c2a%2F%29"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "cursor.ai", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28cursor.ai.one%7C3d5f9121-cb00-484d-ae23-f5649229f368%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C78761ff3-cccb-4c43-8c6f-317205e03d96%2F%29"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "cursor.ai", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28cursor.ai.one%7C3d5f9121-cb00-484d-ae23-f5649229f368%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C89ef35f3-7097-40bb-a064-d5342467bcc9%2F%29"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "cursor.ai", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28cursor.ai.one%7C3d5f9121-cb00-484d-ae23-f5649229f368%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cf39bbd2e-3370-46e4-b5c1-624c357a731d%2F%29"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "cursor.ai", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28cursor.ai.one%7C3d5f9121-cb00-484d-ae23-f5649229f368%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C31ccdcb7-169c-408e-b79d-b07f73a0bbe7%2F%29"}}, {"text": "📖 Overview", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "cursor.ai", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28cursor.ai.one%7C3d5f9121-cb00-484d-ae23-f5649229f368%2F%F0%9F%93%96%20Overview%7Cdf898db4-44a7-40ed-963b-fde7562c29e0%2F%29"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "FAISS", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28FAISS.one%7Cd0c69c33-da1c-4c7c-889c-a37769c6cab6%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Cbb7287a7-6210-488d-b526-8f6d149b1239%2F%29"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "FAISS", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28FAISS.one%7Cd0c69c33-da1c-4c7c-889c-a37769c6cab6%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C566db767-31d3-43cc-ac95-321e57a125d3%2F%29"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "FAISS", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28FAISS.one%7Cd0c69c33-da1c-4c7c-889c-a37769c6cab6%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C8888e971-baf5-4aef-a012-4f936b866936%2F%29"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "FAISS", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28FAISS.one%7Cd0c69c33-da1c-4c7c-889c-a37769c6cab6%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cd5aa7f38-8443-4947-a2e5-feac60587755%2F%29"}}, {"text": "📖 Overview", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "FAISS", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28FAISS.one%7Cd0c69c33-da1c-4c7c-889c-a37769c6cab6%2F%F0%9F%93%96%20Overview%7Cc1abc418-d9a9-44ff-bc83-1a31a98a3060%2F%29"}}, {"text": "Start flow here - Checklist\n\n\n\n\n\nAdd work effort to \"Work Tracking\" List\nA trigger will automatically update the Strategy List\nA trigger will automatically create a OneNote Section with that name in this Notebook, for now you can move it to the right section manually\nA trigger (after a couple of minutes) will create a new work list in the sharepoint site loaded with 100 SDLC items for most product/project work.  Go to Add Column in the view and add the fields you want.  Add/Edit/Delete at will, this is your work list.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "General", "page": "Start flow here - Checklist", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28General.one%7Cff03ae81-7af7-44b3-a065-08530f8ba196%2FStart%20flow%20here%20-%20Checklist%7C7e657325-889f-418f-8a6d-b68d256c2792%2F%29"}}, {"text": "📖 Overview\n\n\n\n\n\nn8n is an open-source workflow automation tool that allows users to connect and automate tasks between different applications and services. Think of it as a more customizable and developer-friendly alternative to tools like Zapier or Make (formerly Integromat). It’s designed to let you create complex workflows that can integrate with APIs, databases, and other systems without writing excessive amounts of code. However, it’s also developer-friendly, allowing you to inject custom code and scripts when needed for advanced use cases.\nOne of the standout features of n8n is its self-hosting capability, giving you full control over your data, which is particularly appealing for businesses concerned about privacy and compliance. n8n supports over 300 pre-built integrations (like Slack, Google Sheets, and HTTP requests) and offers flexibility to connect to virtually any service with its generic HTTP and webhook nodes. Whether you’re automating mundane tasks, syncing data between", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%93%96%20Overview%7Ce4b1b0c2-4aa1-4547-8acb-59f747248669%2F%29"}}, {"text": "ack, Google Sheets, and HTTP requests) and offers flexibility to connect to virtually any service with its generic HTTP and webhook nodes. Whether you’re automating mundane tasks, syncing data between tools, or building advanced workflows with conditional logic and loops, n8n provides a powerful visual interface to get the job done. Its community-driven nature and extensibility make it an ideal choice for developers and non-developers alike.\nImportant Note: The cloud version is paid after the first week, there is a local open source version that I’ve installed, outside of one api issue that couldn’t tunnel into my local version it's been great\n\nThe open source “local” version is easy to install using npx", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "📖 Overview", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%93%96%20Overview%7Ce4b1b0c2-4aa1-4547-8acb-59f747248669%2F%29"}}, {"text": "📜 Known Issues and FAQ's\n\n\n\n\n\nQuestion: Is n8n considered an AI Agent?\n\n\nn8n is not typically considered an AI agent platform, but it can be used to build workflows that include AI agents or connect to AI services.\n\n\nWhat is n8n?\n\n\nIt’s a workflow automation tool.\nOften compared to Zapier or Make (Integromat).\nIt lets you connect APIs, services, and databases using low-code visual flows.\nYou can write custom JavaScript or use HTTP modules to interact with AI APIs like OpenAI, Claude, etc.\n\n\n\nWhat n8n is not:\n\n\nIt’s not an AI agent framework like:￼\n\n<PERSON><PERSON><PERSON><PERSON> (Python/JS)\nAutoGen (Microsoft)\nCrewAI (multi-agent coordination)\nAgentGPT / AutoGPT\n\n\n\n\nThese frameworks are designed to build autonomous or semi-autonomous agents that reason, plan, and act using tools, memory, and objectives.\n\n\nHow n8n can work with AI agents:\n\n\nYou can use n8n to trigger actions based on agent outputs.\nYou can orchestrate an AI workflow with custom logic and persistence.\nGreat for building no-code/low-code wrap", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C42d352fa-3f0c-684f-8704-6f12f76bf27f%2F%29"}}, {"text": "n8n can work with AI agents:\n\n\nYou can use n8n to trigger actions based on agent outputs.\nYou can orchestrate an AI workflow with custom logic and persistence.\nGreat for building no-code/low-code wrappers around more complex AI systems.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "📜 Known Issues and FAQ's", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C42d352fa-3f0c-684f-8704-6f12f76bf27f%2F%29"}}, {"text": "🔧 Setup and Configuration\n\n\n\n\n\nLocal n8n Install - Free\n\nThis is a great approach because it allows you to run n8n without needing to install it globally.\n\nStep 1: Prerequisites\nBefore installing n8n, ensure you have the following installed on your machine:\n1.1 Install Node.js and npm\nn8n requires Node.js 18.x or later. You can check your version with:\nnode -v￼ \nIf you need to install or update Node.js, get it from Node.js official site or use a version manager like nvm:\nnvm install 18￼nvm use 18￼ \nThis also installs npm, which is needed for npx.\n1.2 Install npx (if not already installed)\nnpx comes with npm, but check if it’s available:\nnpx -v￼ \nIf not, install the latest version of npm:\nnpm install -g npm￼ \nStep 2: Running n8n Locally with npx\nOnce your environment is ready, you can run n8n without installing it globally using npx:\nnpx n8n￼ \nThis command will:\n• Download n8n if it’s not cached.\n• Start a local n8n instance.\n🚀 Once it starts, you should see a message like:\nEditor is no", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cfa6e3c9a-cd50-1c42-b542-601fd3da9c18%2F%29"}}, {"text": "8n without installing it globally using npx:\nnpx n8n￼ \nThis command will:\n• Download n8n if it’s not cached.\n• Start a local n8n instance.\n🚀 Once it starts, you should see a message like:\nEditor is now accessible via:￼<http://localhost:5678>￼ \nYou can now open http://localhost:5678 in your browser to access the n8n editor.\nStep 3: Setting Up a Local Database (Optional)\nBy default, n8n runs with a SQLite database stored in ~/.n8n/. If you want a persistent setup, you can use a PostgreSQL database instead.\n3.1 Running with PostgreSQL\nIf you have PostgreSQL installed, create a new database:\ncreatedb n8n_db￼ \nThen start n8n with:\nnpx n8n start --env DB_TYPE=postgresdb --env DB_POSTGRESDB_DATABASE=n8n_db --env DB_POSTGRESDB_USER=your_user --env DB_POSTGRESDB_PASSWORD=your_password￼ \nThis ensures your workflows are stored persistently.\nStep 4: Configuring n8n for Persistent Storage\nSince npx runs n8n in a temporary environment, you might lose configurations when you restart. To persist data,", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🔧 Setup and Configuration", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cfa6e3c9a-cd50-1c42-b542-601fd3da9c18%2F%29"}}, {"text": "s your workflows are stored persistently.\nStep 4: Configuring n8n for Persistent Storage\nSince npx runs n8n in a temporary environment, you might lose configurations when you restart. To persist data, you can specify a data directory:\nnpx n8n start --user-folder ~/.n8n￼ \nThis stores workflows, credentials, and settings in ~/.n8n.\nStep 5: Running n8n in the Background\nTo keep n8n running while you work, you can use:\nnpx n8n start &￼ \nor use a process manager like pm2:\nnpm install -g pm2￼pm2 start npx --name n8n -- n8n start￼pm2 save￼ \nThis ensures n8n restarts automatically.\nStep 6: Setting Up Authentication\nTo enable authentication (so anyone can’t access your n8n instance), set environment variables:\nexport N8N_BASIC_AUTH_ACTIVE=true￼export N8N_BASIC_AUTH_USER=admin￼export N8N_BASIC_AUTH_PASSWORD=securepassword￼npx n8n￼ \nNow, when you visit http://localhost:5678, you’ll need to log in.\nStep 7: Using n8n in Docker (Alternative)\nIf you prefer a Docker-based setup, run:\ndocker run -it --", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🔧 Setup and Configuration", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cfa6e3c9a-cd50-1c42-b542-601fd3da9c18%2F%29"}}, {"text": "PASSWORD=securepassword￼npx n8n￼ \nNow, when you visit http://localhost:5678, you’ll need to log in.\nStep 7: Using n8n in Docker (Alternative)\nIf you prefer a Docker-based setup, run:\ndocker run -it --rm \\\\￼  -p 5678:5678 \\\\￼  -v ~/.n8n:/home/<USER>/.n8n \\\\￼  n8nio/n8n￼ \nThis allows for persistent workflows.\nNext Steps\nNow that n8n is running, try:\n• Creating a simple workflow in the UI.\n• Connecting APIs (like Google Sheets or Slack).\n• Using webhooks to trigger workflows.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🔧 Setup and Configuration", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cfa6e3c9a-cd50-1c42-b542-601fd3da9c18%2F%29"}}, {"text": "💡 Tips Tricks and Shortcuts\n\n\n\n\n\nThings to think about with local n8n – example DayOne integration\n\nThings to think about with local n8n – example DayOne integration\nThe Day One URL scheme is internal and works locally on your macOS or iOS device. If you’re running n8n in the cloud, it won’t be able to directly interact with the Day One URL scheme because it’s designed for local use. However, if you’re running n8n locally (e.g., on your desktop where Day One is installed), you can use the URL scheme effectively.\nHere’s how it works:\n\nLocal n8n Setup\n\n\nEnsure n8n is running on your local computer (e.g., using npx n8n).\nConfirm Day One is installed and accessible on the same machine.\nTriggering Day One with the HTTP Request Node\n\nThe Day One URL scheme can be triggered using an HTTP Request node in n8n. However, since the URL is local, you need a way to open it locally from n8n. This involves using a shell command or AppleScript.\nApproach 1: Execute Shell Commands to Open URLs\n\nAdd an Ex", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Cf7b084eb-7a82-6342-ad79-26cd2548b851%2F%29"}}, {"text": "node in n8n. However, since the URL is local, you need a way to open it locally from n8n. This involves using a shell command or AppleScript.\nApproach 1: Execute Shell Commands to Open URLs\n\nAdd an Execute Command Node in n8n:\nCommand to trigger the URL scheme:\n\nopen \"dayone://post?entry=Your%20entry%20text%20here\"\n2.\tExample Workflow:￼•\tTrigger: A Cron node schedules the workflow.￼•\tExecute Command Node: Runs the open command to trigger the Day One URL.￼ \nApproach 2: Use AppleScript for Advanced Automation\n\nAdd an Execute Command Node to run AppleScript:\n\nosascript -e 'tell application \"Day One\"\nmake new entry with properties {text:\"Your automated entry text\"}\nend tell'\n2.\tExample Workflow:￼•\tTrigger: A Webhook or Cron node schedules the workflow.￼•\tExecute Command Node: Runs the AppleScript to create the journal entry.￼ \nApproach 3: Using Local API with n8n\nIf you want to interact with Day One through an API-like method:\nCreate a small local server or script that listens for n8n HTTP", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Cf7b084eb-7a82-6342-ad79-26cd2548b851%2F%29"}}, {"text": "ipt to create the journal entry.￼ \nApproach 3: Using Local API with n8n\nIf you want to interact with Day One through an API-like method:\nCreate a small local server or script that listens for n8n HTTP requests.\nThe script executes Day One CLI or URL commands based on incoming requests.\nExample flow:\nTrigger Node: An event triggers an HTTP Request node in n8n.\nHTTP Node: Sends a request to your local server.\nLocal Script: Executes the CLI or URL scheme command.\n\nLocal n8n: Ideal for interacting with apps like Day One since everything is on the same machine.\nCloud n8n: Requires an intermediary (like a webhook or local server) to send commands to your desktop.\n\n\n\n\nSteps to Use n8n with Day One URL Scheme\nTo use the Day One URL scheme with n8n, you need to run n8n locally on the same machine where Day One is installed:\nDay One URLs can be opened using macOS’s open command.\nAppleScript provides more control over local macOS apps.\nRunning n8n Locally vs. Cloud", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Cf7b084eb-7a82-6342-ad79-26cd2548b851%2F%29"}}, {"text": "here Day One is installed:\nDay One URLs can be opened using macOS’s open command.\nAppleScript provides more control over local macOS apps.\nRunning n8n Locally vs. Cloud", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Cf7b084eb-7a82-6342-ad79-26cd2548b851%2F%29"}}, {"text": "🧩 Use Cases and Example Code\n\n\n\n\n\nN8n workflows I've built out\nSaturday, March 22, 2025\n9:17 AM\nNOTE: These are just simple examples of what is possible and the different possible hooks available.\nNew gmail add an entry to google Sheets\n\nNew gmail add an entry to google Sheets\n\nhooked up through google cloud console, apis made available\nAI Gmail auto draft response (using openai)\n\nNew gmail email gets an automated draft reply using openai\nGmail Twilio Test\n\nWhen I get an email from <PERSON> I get a text, I have a twilio account with an 800 number\nWeather text\n\nAt 6an using the weather api, if temp is < 40 degrees I get a text saying to wear a jacket\nTodoist To Notion\n\nIntegration between my todolist and Notion, automatically adding to Notion db\nWhen a folder created in OneDrive I get a notification\n\nJust connection to onedrive as part of O365 integration\n\nAI Gmail auto draft response (using openai)\nGmail Twilio Test\nWeather text\nTodoist To Notion\nWhen a folder created in OneDrive I get a", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C29a058cf-b7b0-7a48-ad89-4a02e7f29b66%2F%29"}}, {"text": "cation\n\nJust connection to onedrive as part of O365 integration\n\nAI Gmail auto draft response (using openai)\nGmail Twilio Test\nWeather text\nTodoist To Notion\nWhen a folder created in OneDrive I get a notification\n\n\nExample use cases:\n\nHigh Relevance (Direct Impact on Productivity)\n\nAutomated Task Management: Use n8n to sync <PERSON><PERSON><PERSON> and Todoist, ensuring tasks are automatically updated in both apps.\nDaily Email Digest: Automate a summary of your daily priorities from Todoist and Trello, sent to your email every morning using n8n and email integrations.\nMeeting Prep Workflow: Automate the collection of relevant meeting details (email threads, Trello cards, and Airtable records) into a Google Doc or email.\nProgress Tracking Dashboard: Use Airtable and n8n to generate a weekly progress report, aggregating data from Trello, Todoist, and Google Sheets.\nTask Prioritization with Labels: Create an n8n workflow to auto-prioritize tasks in Todoist based on labels or due dates in Trello.\nCode Execu", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🧩 Use Cases and Example Code", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C29a058cf-b7b0-7a48-ad89-4a02e7f29b66%2F%29"}}, {"text": "aggregating data from Trello, Todoist, and Google Sheets.\nTask Prioritization with Labels: Create an n8n workflow to auto-prioritize tasks in Todoist based on labels or due dates in Trello.\nCode Execution Notifications: Set up <PERSON><PERSON><PERSON> to send you SMS updates when Python or JavaScript scripts complete long-running processes.\nContent Publishing Pipeline: Automate the creation and tracking of blog posts or other content across Trello and Airtable, with publishing deadlines in Todoist.\nClient Communication Alerts: Use Twilio to notify you instantly about critical client emails, based on Gmail filters or Airtable triggers.\nRecurring Task Management: Automate the generation of recurring tasks in Todoist or Trello using n8n to save time on manual input.\nKanban Metrics Tracking: Use n8n to calculate and update performance metrics (e.g., task completion time) for Trello boards in Airtable.\n\nMedium Relevance (Enhanced Efficiency)\n\nCustom CRM Tool: Combine Airtable, Twilio, and n8n to track leads,", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🧩 Use Cases and Example Code", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C29a058cf-b7b0-7a48-ad89-4a02e7f29b66%2F%29"}}, {"text": "e and update performance metrics (e.g., task completion time) for Trello boards in Airtable.\n\nMedium Relevance (Enhanced Efficiency)\n\nCustom CRM Tool: Combine Airtable, Twilio, and n8n to track leads, send follow-ups, and log communication history for freelance or personal projects.\nBug Tracker Integration: Sync GitHub issues or custom Python bug reports with Trello or Airtable for seamless tracking.\nAutomated Data Cleanup: Create Python scripts to clean up Airtable or Google Sheets data, triggered by n8n workflows.\nDeadline Reminders: Set up automated Twilio reminders via SMS for deadlines or meetings synced from Google Calendar and Todoist.\nTime Blocking Assistant: Automate the creation of time blocks in Google Calendar based on your prioritized Todoist or Trello tasks.\nFeedback Collection Tool: Use Airtable and n8n to automate feedback collection (via forms) and track it in Trello or email.\nReading Queue Organizer: Automatically add reading recommendations or saved articles to Airta", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🧩 Use Cases and Example Code", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C29a058cf-b7b0-7a48-ad89-4a02e7f29b66%2F%29"}}, {"text": "on Tool: Use Airtable and n8n to automate feedback collection (via forms) and track it in Trello or email.\nReading Queue Organizer: Automatically add reading recommendations or saved articles to Airtable and track completion with Trello.\nTemplate Generator: Use Python or JavaScript to dynamically create task templates for Trello projects or Airtable workflows.\nLearning Tracker: Set up an Airtable database for tracking your n8n learning progress, with automated updates from Trello or email inputs.\nError Handling Notifications: Use Twilio to receive alerts whenever a coding script or automated workflow fails.\n\nNext Steps\nStart small by implementing one high-impact workflow, such as syncing tasks between Trello and Todoist or setting up a daily email digest. Build incrementally, and incorporate n8n as you gain confidence with the tool.\n\n\nN8n Credentials examples that I have\nSaturday, March 22, 2025\n9:19 AM", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🧩 Use Cases and Example Code", "chunk_index": 4, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C29a058cf-b7b0-7a48-ad89-4a02e7f29b66%2F%29"}}, {"text": "te n8n as you gain confidence with the tool.\n\n\nN8n Credentials examples that I have\nSaturday, March 22, 2025\n9:19 AM", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🧩 Use Cases and Example Code", "chunk_index": 5, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28n8n%20-%20Low%20Code-No%20Code%20tool.one%7Cafce8c55-a0f1-4ccb-97dd-d29425ff2a82%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C29a058cf-b7b0-7a48-ad89-4a02e7f29b66%2F%29"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "OneNote export to FAISS", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28OneNote%20export%20to%20FAISS.one%7C2a42072d-5b69-479b-bd94-d39881d05e55%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cba36a71d-df9e-4cd0-b8e4-c2283a67b4ee%2F%29"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "OneNote export to FAISS", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28OneNote%20export%20to%20FAISS.one%7C2a42072d-5b69-479b-bd94-d39881d05e55%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C48ef7f8d-d93c-473c-bf07-f2aee2e06ee3%2F%29"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "OneNote export to FAISS", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28OneNote%20export%20to%20FAISS.one%7C2a42072d-5b69-479b-bd94-d39881d05e55%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cac4083a6-f892-44fc-a145-5c796f446c83%2F%29"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "OneNote export to FAISS", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28OneNote%20export%20to%20FAISS.one%7C2a42072d-5b69-479b-bd94-d39881d05e55%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cb645e6c6-e42c-44d8-a9ca-537c0d2a7262%2F%29"}}, {"text": "📖 Overview", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "OneNote export to FAISS", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28OneNote%20export%20to%20FAISS.one%7C2a42072d-5b69-479b-bd94-d39881d05e55%2F%F0%9F%93%96%20Overview%7C8fe4c825-06d8-41db-941f-e98aaaa4e7fb%2F%29"}}, {"text": "📖 Overview", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG on AWS", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28RAG%20on%20AWS.one%7C941e9c0e-f6dc-4371-b859-3dcd24a02d23%2F%F0%9F%93%96%20Overview%7C01288094-ed3b-b84e-8a4b-8947f08bc48a%2F%29"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG on AWS", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28RAG%20on%20AWS.one%7C941e9c0e-f6dc-4371-b859-3dcd24a02d23%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Ce9b4eb0c-362d-394a-b187-dbcf7f9f458d%2F%29"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG on AWS", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28RAG%20on%20AWS.one%7C941e9c0e-f6dc-4371-b859-3dcd24a02d23%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C8a152a6c-7bec-5149-b633-93508c34e178%2F%29"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG on AWS", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28RAG%20on%20AWS.one%7C941e9c0e-f6dc-4371-b859-3dcd24a02d23%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Ce9e6a1ea-3578-004d-b3f5-33b86fb6115e%2F%29"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG on AWS", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28RAG%20on%20AWS.one%7C941e9c0e-f6dc-4371-b859-3dcd24a02d23%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C3758b468-8c68-f14d-8b41-931f752435bd%2F%29"}}, {"text": "Read Me – How to use and contribute to this notebook\n\n\n\n\n\nWelcome to the Creospan Knowledge Exchange! This shared OneNote notebook is designed to organize, centralize, and continuously evolve our collective understanding of AI tools, platforms, and practices. The structure of this notebook is built for clarity, collaboration, and scalability. Please follow the organizational guidelines below when adding new content to keep the notebook clean and easy to navigate.\n\nNotebook Structure\nThis notebook uses a Notebook → Sections → Pages structure. Each section represents a major category in the AI landscape—such as Models, Low Code/No Code Tools, Agents, IDEs, and more. Within each section, individual pages are dedicated to specific tools, frameworks, or research topics (e.g., n8n, Cursor.ai, AutoGPT). If you have deeper content—like tutorials, comparisons, or implementation notes—you can add subpages beneath the main page for that tool. Please label pages clearly, and include tags or labels", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "READ ME FIRST", "page": "Read Me – How to use and contribute to this notebook", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28READ%20ME%20FIRST.one%7Cc0411380-b37e-4c63-8a8e-0fd89ae0fdc2%2FRead%20Me%20%E2%80%93%20How%20to%20use%20and%20contribute%20to%20this%20%7C5ac8390a-d5f2-4a67-8120-5746abc7a406%2F%29"}}, {"text": "toGPT). If you have deeper content—like tutorials, comparisons, or implementation notes—you can add subpages beneath the main page for that tool. Please label pages clearly, and include tags or labels where appropriate to improve searchability. If you’re unsure where something belongs, start by adding it to the Miscellaneous section with a note, and we’ll help re-categorize it.\n\nThis is a living resource. Keep your contributions focused and well-organized. Use bullet points, headers, and links for readability. Reference sources when possible, and link to external documents, GitHub repos, or videos to provide additional context. Let’s build something useful and scalable together!\n\nSome Examples to show our intention:\nOllama  (Web view)\n📖 Figma file to next.js/react Overview (Web view)\nFabric AI Patterns  (Web view)\nFull AI Prompt Database - Jeff Su  (Web view)\nn8n - Low Code-No Code tool  (Web view)\n\n\nExample in the Use Cases and Example Code section, of code in our github:RAG-PythonExa", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "READ ME FIRST", "page": "Read Me – How to use and contribute to this notebook", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28READ%20ME%20FIRST.one%7Cc0411380-b37e-4c63-8a8e-0fd89ae0fdc2%2FRead%20Me%20%E2%80%93%20How%20to%20use%20and%20contribute%20to%20this%20%7C5ac8390a-d5f2-4a67-8120-5746abc7a406%2F%29"}}, {"text": "c AI Patterns  (Web view)\nFull AI Prompt Database - <PERSON>  (Web view)\nn8n - Low Code-No Code tool  (Web view)\n\n\nExample in the Use Cases and Example Code section, of code in our github:RAG-PythonExamples", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "READ ME FIRST", "page": "Read Me – How to use and contribute to this notebook", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28READ%20ME%20FIRST.one%7Cc0411380-b37e-4c63-8a8e-0fd89ae0fdc2%2FRead%20Me%20%E2%80%93%20How%20to%20use%20and%20contribute%20to%20this%20%7C5ac8390a-d5f2-4a67-8120-5746abc7a406%2F%29"}}, {"text": "📖 Overview", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28V.0.one%7C244783e2-a59d-443e-8542-4c9270d33cfa%2F%F0%9F%93%96%20Overview%7Cb1daf0b7-4e7b-2744-a862-3e3d099eb3c4%2F%29"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28V.0.one%7C244783e2-a59d-443e-8542-4c9270d33cfa%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Ccefbd5a5-17a2-9e47-92d0-cac718009e06%2F%29"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28V.0.one%7C244783e2-a59d-443e-8542-4c9270d33cfa%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C1da1022b-f874-4549-b9e7-1b458752a3ed%2F%29"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28V.0.one%7C244783e2-a59d-443e-8542-4c9270d33cfa%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C7bf03b0b-32de-2a42-aaeb-78b0515da655%2F%29"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28V.0.one%7C244783e2-a59d-443e-8542-4c9270d33cfa%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cfcee1217-9956-1f4c-819d-1aee5697a837%2F%29"}}, {"text": "🧩 Use Cases and Example Code\n\n\n\n\n\nIn Python, using browser-use to scrap web sites:\n\nThis simple code will take that hard coded task and do the work for you.\n\nfrom langchain_openai import ChatOpenAI\n\nfrom browser_use import Agent\nfrom dotenv import load_dotenv\nload_dotenv()\n\nimport asyncio\n\nllm = ChatOpenAI(model=\"gpt-4o\")\n\nasync def main():\n    agent = Agent(\n task=\"Compare the price of gpt-4o and DeepSeek-V3\",\n llm=llm,\n    )\n    result = await agent.run()\n print(result)\n\nasyncio.run(main())\n\n\nExample Output from the simple code above:\nINFO     [browser_use] BrowserUse logging setup complete with level info\nINFO     [root] Anonymized telemetry enabled. See https://docs.browser-use.com/development/telemetry for more information.\n/Users/<USER>/PycharmProjects/browser-use/.venv1/lib/python3.11/site-packages/browser_use/agent/message_manager/views.py:59: LangChainBetaWarning: The function `load` is in beta. It is actively being worked on, so the API may change.\n  value['message'] = load(va", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca2272ad7-6c9d-f04f-a124-11f654dab9bb%2F%29", "section_group": "Agentic AI"}}, {"text": "1/site-packages/browser_use/agent/message_manager/views.py:59: LangChainBetaWarning: The function `load` is in beta. It is actively being worked on, so the API may change.\n  value['message'] = load(value['message'])\nINFO     [agent] 🚀 Starting task: Compare the price of gpt-4o and DeepSeek-V3\nINFO     [agent] 📍 Step 1\nINFO     [agent] 🤷 Eval: N/A\nINFO     [agent] 🧠 Memory: Starting the task to compare prices of gpt-4o and DeepSeek-V3.\nINFO     [agent] 🎯 Next goal: Search for the prices of gpt-4o and DeepSeek-V3 online.\nINFO     [agent] 🛠️  Action 1/1: {\"search_google\":{\"query\":\"gpt-4o price\"}}\nINFO     [controller] 🔍  Searched for \"gpt-4o price\" in Google\nINFO     [agent] 📍 Step 2\nINFO     [agent] 👍 Eval: Success - Searched for gpt-4o prices and identified elements with potential information.\nINFO     [agent] 🧠 Memory: gpt-4o prices are being identified. Now need to search for DeepSeek-V3 prices.\nINFO     [agent] 🎯 Next goal: Extract price information for GPT-4o and then search for <PERSON>", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca2272ad7-6c9d-f04f-a124-11f654dab9bb%2F%29", "section_group": "Agentic AI"}}, {"text": "on.\nINFO     [agent] 🧠 Memory: gpt-4o prices are being identified. Now need to search for DeepSeek-V3 prices.\nINFO     [agent] 🎯 Next goal: Extract price information for GPT-4o and then search for DeepSeek-V3 prices.\nINFO     [agent] 🛠️  Action 1/1: {\"extract_content\":{\"goal\":\"Extract GPT-4o pricing information from the search results.\"}}\nINFO     [controller] 📄  Extracted from page\n: ```json\n{\n  \"pricing_information\": [\n    {\n      \"source\": \"OpenAI API Pricing\",\n      \"prices\": {\n        \"GPT-4o mini\": {\n          \"input_price_per_million_tokens\": \"$0.150\",\n          \"fine_tuning_price_per_million_tokens\": \"$0.30\"\n        },\n        \"general\": \"Other prices are not specified in detail.\"\n      }\n    },\n    {\n      \"source\": \"Azure OpenAI Service - Pricing\",\n      \"prices\": {\n        \"general\": {\n          \"global_input_price\": \"$2.50\",\n          \"cached_input_price\": \"$1.25\",\n          \"output_price\": \"$10\"\n        }\n      }\n    },\n    {\n      \"source\": \"Reddit - ChatGPTPro\",\n      \"p", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca2272ad7-6c9d-f04f-a124-11f654dab9bb%2F%29", "section_group": "Agentic AI"}}, {"text": "ral\": {\n          \"global_input_price\": \"$2.50\",\n          \"cached_input_price\": \"$1.25\",\n          \"output_price\": \"$10\"\n        }\n      }\n    },\n    {\n      \"source\": \"Reddit - ChatGPTPro\",\n      \"prices\": {\n        \"general\": {\n          \"input_price_per_million_tokens\": \"$5\",\n          \"output_price_per_million_tokens\": \"$15\",\n          \"combined_input_output_price_per_million_tokens\": \"$20\"\n        }\n      }\n    },\n    {\n      \"source\": \"Nebuly-AI\",\n      \"prices\": {\n        \"general\": {\n          \"input_price_per_million_tokens\": \"$3\",\n          \"output_price_per_million_tokens\": \"$10\"\n        }\n      }\n    },\n    {\n      \"source\": \"Artificial Analysis\",\n      \"prices\": {\n        \"general\": {\n          \"price_per_million_tokens\": \"$4.38\",\n          \"input_price_per_million_tokens\": \"$2.50\",\n          \"output_price_per_million_tokens\": \"Price not specified\"\n        }\n      }\n    },\n    {\n      \"source\": \"Neowin\",\n      \"prices\": {\n        \"general\": {\n          \"input_price_per_mi", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca2272ad7-6c9d-f04f-a124-11f654dab9bb%2F%29", "section_group": "Agentic AI"}}, {"text": "$2.50\",\n          \"output_price_per_million_tokens\": \"Price not specified\"\n        }\n      }\n    },\n    {\n      \"source\": \"Neowin\",\n      \"prices\": {\n        \"general\": {\n          \"input_price_per_million_tokens\": \"$2.50\",\n          \"output_price_per_million_tokens\": \"$10\"\n        }\n      }\n    },\n    {\n      \"source\": \"LLM Price Check\",\n      \"prices\": {\n        \"GPT-4o mini\": {\n          \"input_price_per_million_tokens\": \"$0.15\",\n          \"output_price_per_million_tokens\": \"$0.60\"\n        }\n      }\n    }\n  ]\n}\n```\n\nINFO     [agent] 📍 Step 3\nINFO     [agent] 👍 Eval: Success - Extracted GPT-4o pricing information.\nINFO     [agent] 🧠 Memory: Extracted GPT-4o pricing information. Preparing to search DeepSeek-V3 prices next.\nINFO     [agent] 🎯 Next goal: Search for the price of DeepSeek-V3.\nINFO     [agent] 🛠️  Action 1/1: {\"search_google\":{\"query\":\"DeepSeek-V3 price\"}}\nINFO     [controller] 🔍  Searched for \"DeepSeek-V3 price\" in Google\nINFO     [agent] 📍 Step 4\nINFO     [agent] 👍 Eval:", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 4, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca2272ad7-6c9d-f04f-a124-11f654dab9bb%2F%29", "section_group": "Agentic AI"}}, {"text": "INFO     [agent] 🛠️  Action 1/1: {\"search_google\":{\"query\":\"DeepSeek-V3 price\"}}\nINFO     [controller] 🔍  Searched for \"DeepSeek-V3 price\" in Google\nINFO     [agent] 📍 Step 4\nINFO     [agent] 👍 Eval: Success - Searched for DeepSeek-V3 prices and identified potential pricing information in search results.\nINFO     [agent] 🧠 Memory: Extracted GPT-4o pricing information. Now, searching and extracting DeepSeek-V3 prices from the results.\nINFO     [agent] 🎯 Next goal: Extract price information for DeepSeek-V3.\nINFO     [agent] 🛠️  Action 1/1: {\"extract_content\":{\"goal\":\"Extract DeepSeek-V3 pricing information from the search results.\"}}\nINFO     [controller] 📄  Extracted from page\n: ```json\n{\n    \"DeepSeek-V3 Pricing Information\": [\n        {\n            \"source\": \"DeepSeek API Docs\",\n            \"url\": \"https://api-docs.deepseek.com/quick_start/pricing\",\n            \"details\": [\n                {\n                    \"standard_price_cache_hit\": \"$0.07 per 1M tokens (UTC 00:30-16:30)\",", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 5, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca2272ad7-6c9d-f04f-a124-11f654dab9bb%2F%29", "section_group": "Agentic AI"}}, {"text": "\"url\": \"https://api-docs.deepseek.com/quick_start/pricing\",\n            \"details\": [\n                {\n                    \"standard_price_cache_hit\": \"$0.07 per 1M tokens (UTC 00:30-16:30)\",\n                    \"standard_price_cache_miss\": \"$0.14 per 1M tokens (UTC 00:30-16:30)\"\n                }\n            ]\n        },\n        {\n            \"source\": \"Artificial Analysis\",\n            \"url\": \"https://artificialanalysis.ai/models/deepseek-v3\",\n            \"details\": [\n                {\n                    \"input_token_price\": \"$0.27 per 1M tokens\",\n                    \"output_token_price\": \"Blended 3:1\"\n                },\n                {\n                    \"note\": \"Cheaper compared to average with a price of $0.48 per 1M Tokens (blended 3:1).\"\n                }\n            ]\n        },\n        {\n            \"source\": \"South China Morning Post\",\n            \"url\": \"https://www.scmp.com/tech/big-tech/article/3298101/deepseek-ends-promotional-pricing-amid-surging-popularity-secur", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 6, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca2272ad7-6c9d-f04f-a124-11f654dab9bb%2F%29", "section_group": "Agentic AI"}}, {"text": "},\n        {\n            \"source\": \"South China Morning Post\",\n            \"url\": \"https://www.scmp.com/tech/big-tech/article/3298101/deepseek-ends-promotional-pricing-amid-surging-popularity-secures-aicom-redirect\",\n            \"details\": [\n                {\n                    \"new_chat_pricing_input\": \"$0.27 per million token inputs\",\n                    \"new_chat_pricing_output\": \"$1.1 per million token outputs\"\n                }\n            ]\n        },\n        {\n            \"source\": \"Substack - Don't Worry About the Vase\",\n            \"url\": \"https://thezvi.substack.com/p/deekseek-v3-the-six-million-dollar\",\n            \"details\": [\n                {\n                    \"inference_cost\": \"$0.14/$0.28 per million tokens\",\n                    \"comparison\": \"Similar to Gemini Flash, high end $3/$15 for Claude Sonnet\"\n                }\n            ]\n        }\n    ]\n}\n```\n\nINFO     [agent] 📍 Step 5\nINFO     [agent] 👍 Eval: Success - Retrieved pricing information for both GPT-4o an", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 7, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca2272ad7-6c9d-f04f-a124-11f654dab9bb%2F%29", "section_group": "Agentic AI"}}, {"text": "high end $3/$15 for <PERSON>\"\n                }\n            ]\n        }\n    ]\n}\n```\n\nINFO     [agent] 📍 Step 5\nINFO     [agent] 👍 Eval: Success - Retrieved pricing information for both GPT-4o and DeepSeek-V3.\nINFO     [agent] 🧠 Memory: Completed gathering pricing information for GPT-4o and DeepSeek-V3. Ready to compare and conclude.\nINFO     [agent] 🎯 Next goal: Compare the extracted pricing information for GPT-4o and DeepSeek-V3.\nINFO     [agent] 🛠️  Action 1/1: {\"done\":{\"text\":\"Comparison of prices:\\n\\n**GPT-4o Prices:**\\n- Input Price: $0.15 - $5 per million tokens (varies by source)\\n- Output Price: Up to $15 per million tokens\\n- Specific models like GPT-4o Mini: $0.15 (input) & $0.60 (output) per million tokens\\n\\n**DeepSeek-V3 Prices:**\\n- Cache Hit Input Price: $0.07 per million tokens\\n- Cache Miss Input Price: $0.14 per million tokens\\n- General Input Price: $0.27 per million tokens\\n- Output Price: Around $1.1 per million tokens (blended 3:1 costs $0.48)\\n\\nConclusion:", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 8, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca2272ad7-6c9d-f04f-a124-11f654dab9bb%2F%29", "section_group": "Agentic AI"}}, {"text": "ion tokens\\n- Cache Miss Input Price: $0.14 per million tokens\\n- General Input Price: $0.27 per million tokens\\n- Output Price: Around $1.1 per million tokens (blended 3:1 costs $0.48)\\n\\nConclusion: GPT-4o generally has a wider price range and can be more expensive, especially concerning output costs, whereas DeepSeek-V3 offers potentially lower costs for token inputs and blended pricing for outputs.\",\"success\":true}}\nINFO     [agent] 📄 Result: Comparison of prices:\n\n**GPT-4o Prices:**\n- Input Price: $0.15 - $5 per million tokens (varies by source)\n- Output Price: Up to $15 per million tokens\n- Specific models like GPT-4o Mini: $0.15 (input) & $0.60 (output) per million tokens\n\n**DeepSeek-V3 Prices:**\n- Cache Hit Input Price: $0.07 per million tokens\n- Cache Miss Input Price: $0.14 per million tokens\n- General Input Price: $0.27 per million tokens\n- Output Price: Around $1.1 per million tokens (blended 3:1 costs $0.48)\n\nConclusion: GPT-4o generally has a wider price range and can be", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 9, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca2272ad7-6c9d-f04f-a124-11f654dab9bb%2F%29", "section_group": "Agentic AI"}}, {"text": "illion tokens\n- General Input Price: $0.27 per million tokens\n- Output Price: Around $1.1 per million tokens (blended 3:1 costs $0.48)\n\nConclusion: GPT-4o generally has a wider price range and can be more expensive, especially concerning output costs, whereas DeepSeek-V3 offers potentially lower costs for token inputs and blended pricing for outputs.\nINFO     [agent] ✅ Task completed\nINFO     [agent] ✅ Successfully\n\n\nThere is more output but just in more detail regarding what is happening.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 10, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca2272ad7-6c9d-f04f-a124-11f654dab9bb%2F%29", "section_group": "Agentic AI"}}, {"text": "📖 Overview\n\n\n\n\n\n🤖 What Are AI Agents?\n\nAt a basic level, an AI agent is a computer program that acts intelligently in a given environment to reach a goal. Think of it like a smart robot (real or virtual) that senses what’s going on, makes decisions, and takes actions to achieve something — whether it’s cleaning a room, playing a game, or recommending a movie.\n\nJust like a person, an AI agent has three main parts:\n\n\nPerception – it observes the environment through input (like a robot using sensors or a chatbot reading your message).\nDecision-making – it thinks or processes that input to decide what to do next.\nAction – it does something based on that decision (like moving, speaking, or changing a value).\n\n\n\n\n🧠 Types of AI Agents\n\nNot all AI agents are created equal. Some are super simple, like:\n\n\nReflex agents – they just react to what they see, without thinking about the future. Like a thermostat turning on the heat when it’s too cold.\n\n\nOthers are smarter:\n\n\nModel-based agents – they", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%93%96%20Overview%7C693e3694-7042-5f46-8c50-ba892cdb809b%2F%29", "section_group": "Agentic AI"}}, {"text": "ike:\n\n\nReflex agents – they just react to what they see, without thinking about the future. Like a thermostat turning on the heat when it’s too cold.\n\n\nOthers are smarter:\n\n\nModel-based agents – they keep an internal model of the world and try to plan ahead.\nGoal-based agents – they make decisions that help them reach a specific goal.\nLearning agents – these improve over time by learning from past experiences, just like how we get better at games or cooking with practice!\n\n\n\n\n🏃 Real-World Examples\n\n\nA self-driving car is a complex AI agent. It senses traffic, predicts what other drivers will do, and decides how to move safely.\nA video game enemy that adapts to your playing style is also an AI agent.\nEven a smart assistant like <PERSON><PERSON> or <PERSON><PERSON> is an AI agent — it hears your request, figures out what you mean, and replies or takes action.\n\n\n\nIn short, AI agents are like little digital brains that can see, decide, and act. Some are simple, others are more advanced, but they’re all around us", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "📖 Overview", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%93%96%20Overview%7C693e3694-7042-5f46-8c50-ba892cdb809b%2F%29", "section_group": "Agentic AI"}}, {"text": "ut what you mean, and replies or takes action.\n\n\n\nIn short, AI agents are like little digital brains that can see, decide, and act. Some are simple, others are more advanced, but they’re all around us, quietly helping (or challenging!) us in everyday life.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "📖 Overview", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%93%96%20Overview%7C693e3694-7042-5f46-8c50-ba892cdb809b%2F%29", "section_group": "Agentic AI"}}, {"text": "🔧 Setup and Configuration\n\n\n\n\n\n\n\n\nhttps://openai.github.io/openai-agents-python/\n\n\nAbsolutely! Let’s talk about how you set up and configure an AI agent — and don’t worry, I’ll keep it simple and sprinkle in some examples to make it click. 😄\n\n\n\n🧰 Basic Setup of an AI Agent\n\nSetting up an AI agent usually means giving it:\n\n\nAn environment to operate in\nSensors (inputs) to perceive the environment\nA decision-making brain (logic or a model)\nActuators (outputs) to interact with the environment\n\n\nIn code, this often involves creating classes or functions that simulate both the agent and the environment.\n\n\n\n🔧 Example 1: Reflex Agent in a Simple Grid World\n\nLet’s say we have a very basic robot vacuum that moves around a grid and cleans dirty cells.\n\n\nEnvironment Setup\nenvironment = {￼    (0, 0): 'dirty',￼    (0, 1): 'clean',￼    (1, 0): 'dirty',￼    (1, 1): 'clean',￼}\n\nReflex Agent Setup\nclass ReflexAgent:￼    def __init__(self, position):￼        self.position = position￼\ndef perceive(self,", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cd5d0d1c5-a5a2-1e47-bd94-ef9bf5f8b427%2F%29", "section_group": "Agentic AI"}}, {"text": "'dirty',￼    (0, 1): 'clean',￼    (1, 0): 'dirty',￼    (1, 1): 'clean',￼}\n\nReflex Agent Setup\nclass ReflexAgent:￼    def __init__(self, position):￼        self.position = position￼\ndef perceive(self, environment):￼        return environment[self.position]￼\ndef decide(self, percept):￼        if percept == 'dirty':￼            return 'clean'￼        else:￼            return 'move'￼\ndef act(self, action):￼        if action == 'clean':￼            print(\"Cleaning...\")￼        elif action == 'move':￼            print(\"Moving to next spot...\")\n\nUsing the Agent\nagent = ReflexAgent((0, 0))￼percept = agent.perceive(environment)￼action = agent.decide(percept)￼agent.act(action)\nThis simple setup shows how an agent can sense, decide, and act.\n\n\n\n🧠 Example 2: Goal-Based Agent Using Search\n\nNow let’s imagine a maze. The agent’s goal is to find the exit.\nclass MazeAgent:￼    def __init__(self, maze, start, goal):￼        self.maze = maze￼        self.position = start￼        self.goal = goal￼\ndef is_", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🔧 Setup and Configuration", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cd5d0d1c5-a5a2-1e47-bd94-ef9bf5f8b427%2F%29", "section_group": "Agentic AI"}}, {"text": "agine a maze. The agent’s goal is to find the exit.\nclass MazeAgent:￼    def __init__(self, maze, start, goal):￼        self.maze = maze￼        self.position = start￼        self.goal = goal￼\ndef is_goal(self, pos):￼        return pos == self.goal￼\ndef get_possible_moves(self, pos):￼        # Example logic: return adjacent positions that are open￼        return [move for move in self.maze.get(pos, [])]￼\ndef search(self):￼        from collections import deque￼        visited = set()￼        queue = deque([(self.position, [self.position])])￼\nwhile queue:￼            current, path = queue.popleft()￼            if self.is_goal(current):￼                return path￼            for move in self.get_possible_moves(current):￼                if move not in visited:￼                    visited.add(move)￼                    queue.append((move, path + [move]))￼        return None\nYou’d run agent.search() to find a path to the goal. This is more of a goal-based agent, planning its way to victory.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🔧 Setup and Configuration", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cd5d0d1c5-a5a2-1e47-bd94-ef9bf5f8b427%2F%29", "section_group": "Agentic AI"}}, {"text": "move)￼                    queue.append((move, path + [move]))￼        return None\nYou’d run agent.search() to find a path to the goal. This is more of a goal-based agent, planning its way to victory. 🏆\n\n\n\n⚙️ Configuration Tips\n\n\nYou define the rules of the world/environment (like how a maze is built or how dirt appears).\nYou program the agent’s behavior strategy (simple reflex, learning, or planning).\nYou may also include parameters like learning rates, move costs, or decision thresholds if the agent is more advanced.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🔧 Setup and Configuration", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cd5d0d1c5-a5a2-1e47-bd94-ef9bf5f8b427%2F%29", "section_group": "Agentic AI"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C14e9a690-dcdb-2146-b328-ec37e331ed71%2F%29", "section_group": "Agentic AI"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C59e08bc1-4808-5341-9ce6-67d0aa083a3b%2F%29", "section_group": "Agentic AI"}}, {"text": "OpenAi Agents SDK", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "OpenAi Agents SDK", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FAgents.one%7Cd057a4c3-fd49-514d-80cd-45213c24b8db%2FOpenAi%20Agents%20SDK%7C5ad14853-f588-ec4e-b597-11f0108be077%2F%29", "section_group": "Agentic AI"}}, {"text": "📖 Overview – Model Context Protocol\n\n\n\n\n\n\nModel context protocol refers to the way an AI model maintains and uses information across a conversation to deliver more coherent, personalized, and context-aware responses. It defines how memory, message history, and explicitly shared user data are handled to inform the model’s behavior.\nAt a high level, context in a conversational model is built from three layers:\nMessage History: This includes the ongoing exchange between the user and the assistant. It helps the model stay aware of the current topic, respond appropriately, and avoid repeating information. This history is transient—it only persists for the duration of a conversation session.\nSystem and Model Instructions: These are internal prompts that guide the model’s personality, behavior, and boundaries. For example, they might include settings like “respond in a friendly tone” or “do not provide medical advice.” These are generally static for a given session but can vary by user type o", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "📖 Overview – Model Context Protocol", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FMCP.one%7Ce70f60c6-a80b-40f8-9b37-2f33842a1879%2F%F0%9F%93%96%20Overview%20%E2%80%93%20Model%20Context%20Protocol%7Cdb73ca2a-8695-4c25-85ed-149433d93afd%2F%29", "section_group": "Agentic AI"}}, {"text": "nd boundaries. For example, they might include settings like “respond in a friendly tone” or “do not provide medical advice.” These are generally static for a given session but can vary by user type or application.\nPersistent Memory (User Context): This includes information a user has shared and chosen to be remembered, such as their name, preferences, goals, or past projects. In ChatGPT, this memory is explicitly editable by the user and designed to support longer-term continuity. The model uses this to offer personalized suggestions and follow up intelligently on prior topics.\n\nThe protocol ensures the model stays on topic, respects user instructions, and delivers responses with continuity and relevance. It’s like the model keeping a “working memory” for the session, while also referring to a longer-term “notebook” of what it knows about you—if and only if you’ve allowed it to.\n\n\nMCP (Model Context Protocol) is a protocol. It's an open standard for how MCP clients/servers can interac", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "📖 Overview – Model Context Protocol", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FMCP.one%7Ce70f60c6-a80b-40f8-9b37-2f33842a1879%2F%F0%9F%93%96%20Overview%20%E2%80%93%20Model%20Context%20Protocol%7Cdb73ca2a-8695-4c25-85ed-149433d93afd%2F%29", "section_group": "Agentic AI"}}, {"text": "to a longer-term “notebook” of what it knows about you—if and only if you’ve allowed it to.\n\n\nMCP (Model Context Protocol) is a protocol. It's an open standard for how MCP clients/servers can interact.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "📖 Overview – Model Context Protocol", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FMCP.one%7Ce70f60c6-a80b-40f8-9b37-2f33842a1879%2F%F0%9F%93%96%20Overview%20%E2%80%93%20Model%20Context%20Protocol%7Cdb73ca2a-8695-4c25-85ed-149433d93afd%2F%29", "section_group": "Agentic AI"}}, {"text": "📜 Known Issues and FAQs\n\n\n\n\n\na) Authentication: How will you determine who has access to what capabilities?\nb) Trust: How do you determine which MCP servers are trust-worthy enough to use? \nc) Provisioning: MCP servers are currently often offered as a GitHub repo, but you're responsible for self-hosting the code.\nd) Security: Because of the way LLMs interact with tools made available by an MCP server, there are some new risks. That's why it's important to use known clients with trusted servers.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "📜 Known Issues and FAQs", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FMCP.one%7Ce70f60c6-a80b-40f8-9b37-2f33842a1879%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQs%7Cd10c2bd7-474d-0b48-b07e-31b8c8ceaaa3%2F%29", "section_group": "Agentic AI"}}, {"text": "🧩 Use Cases and Example Code\n\n\n\n\n\nThere is an mcp example of using Github and allowing the following commands (at this time), from natural language processing:\n\nThe repository is here: https://github.com/github/github-mcp-server\n\nHere is a tutorial but note that cursor.ai gets updated frequently:\nConnect GitHub MCP server with Cursor AI using this Cursor MCP setup.\n\n\n\n\nNote: I used the command in the .sh file as the mcp.json didn't seem to pick up my github token properly, kept getting errors, but I did get this to work.\n\nI got it working, but the tutorial seems to be with an older version of cursor.  When creating the mcp server from the cursor settings your json should like like this: but put in your own github token in the .sh file.\n \nRun.sh file:\nGITHUB_PERSONAL_ACCESS_TOKEN=<your token> npx -y @modelcontextprotocol/server-github\n\n\n{\n  \"mcpServers\": {\n    \"github\": {\n      \"command\": \"bash /Users/<USER>/ailearning/mcp/servers-main/run.sh\",\n      \"args\": [\n        \"\"\n      ],\n      \"e", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FMCP.one%7Ce70f60c6-a80b-40f8-9b37-2f33842a1879%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cdf20af88-884d-9346-81bc-4b2fd055fbec%2F%29", "section_group": "Agentic AI"}}, {"text": "n> npx -y @modelcontextprotocol/server-github\n\n\n{\n  \"mcpServers\": {\n    \"github\": {\n      \"command\": \"bash /Users/<USER>/ailearning/mcp/servers-main/run.sh\",\n      \"args\": [\n        \"\"\n      ],\n      \"env\": {\n        \"GITHUB_PERSONAL_ACCESS_TOKEN\": \"\"\n      }\n    }\n  }\n}\n\nNOTE:  I have a couple hundred repositories in my github account, and this choked when listing all of them…\n\ncreate_or_update_file, search_repositories, create_repository, get_file_contents, push_files, create_issue, create_pull_request, fork_repository, create_branch, list_commits, list_issues, update_issue, add_issue_comment, search_code, search_issues, search_users, get_issue,  get_pull_request, list_pull_requests, create_pull_request_review, merge_pull_request\nget_pull_request_files, get_pull_request_status, update_pull_request_branch\n\n\n\n\nLangchain has a repository with a number of MCP examples, most notably an example of creating an agent that has access to your local file system, all locally.\n\nhttps://github.com/l", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "🧩 Use Cases and Example Code", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FMCP.one%7Ce70f60c6-a80b-40f8-9b37-2f33842a1879%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cdf20af88-884d-9346-81bc-4b2fd055fbec%2F%29", "section_group": "Agentic AI"}}, {"text": "_request_branch\n\n\n\n\nLangchain has a repository with a number of MCP examples, most notably an example of creating an agent that has access to your local file system, all locally.\n\nhttps://github.com/langchain-ai/langchainjs-mcp-adapters/tree/main", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "🧩 Use Cases and Example Code", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FMCP.one%7Ce70f60c6-a80b-40f8-9b37-2f33842a1879%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cdf20af88-884d-9346-81bc-4b2fd055fbec%2F%29", "section_group": "Agentic AI"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FMCP.one%7Ce70f60c6-a80b-40f8-9b37-2f33842a1879%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C714e69e6-8996-5145-9bd6-ced3491b393b%2F%29", "section_group": "Agentic AI"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Agentic%20AI%2FMCP.one%7Ce70f60c6-a80b-40f8-9b37-2f33842a1879%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C02c37f33-0c24-6f4e-b2bc-13e7f874c9ae%2F%29", "section_group": "Agentic AI"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n\n\n\nThis is to be an example of modernizing a 6 year old code base to next.js and then to mobile.  I created this to be able to memorize multiple decks of cards and testing same.  \n\nIt's pretty lame but gives me an example of using different ai tools in order to modernize and make a code base higher quailty.  \n\nIt's far from done yet.\n\nThe current code bases are in the Use Cases and Example Code Sections", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CardRandomizer_nextjs_port", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FCardRandomizer_nextjs_port.one%7C94c0d05d-5a89-4857-9bd3-8a2def095e41%2F%F0%9F%93%96%20Overview%7Cb698a46c-48f7-2e41-a67f-0cf21e40b463%2F%29", "section_group": "Applications"}}, {"text": "🧩 Use Cases and Example Code\n\n\n\n\n\n\n\n\nThe nextjs code is here: Link to code\n\n\nThe 6 year old node.js code is here: Link to code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CardRandomizer_nextjs_port", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FCardRandomizer_nextjs_port.one%7C94c0d05d-5a89-4857-9bd3-8a2def095e41%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C0aacedd6-a753-9846-8cc7-a308aa838978%2F%29", "section_group": "Applications"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CardRandomizer_nextjs_port", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FCardRandomizer_nextjs_port.one%7C94c0d05d-5a89-4857-9bd3-8a2def095e41%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C4306d8ed-b652-0545-a44f-61bb31be107f%2F%29", "section_group": "Applications"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CardRandomizer_nextjs_port", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FCardRandomizer_nextjs_port.one%7C94c0d05d-5a89-4857-9bd3-8a2def095e41%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C4f6f5e28-f271-534a-b49e-758b754cbfdc%2F%29", "section_group": "Applications"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CardRandomizer_nextjs_port", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FCardRandomizer_nextjs_port.one%7C94c0d05d-5a89-4857-9bd3-8a2def095e41%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cdf71667c-3d97-e245-a287-386514dd737b%2F%29", "section_group": "Applications"}}, {"text": "🧩 Use Cases and Example Code\n\n\n\n\nI used this command to zip the code, except for the node_modules directory so that I could have chatgpt analyze it.\n\n\nThen I used this prompt:\n\nI would like you to look at this code, reverse engineer it, and create 50 use cases that a Product Manager could use to create a code base like this, then please add 50 more use cases on making it better.  Please put it in a markdown file with this as an example:\n\n### **Functional Use Cases**\n\n1. **Start Card Memorization Session**\n   - Actor: User\n   - Preconditions: User is on the main page.\n   - Main Flow: User clicks \"Start Session.\"\n   - Postconditions: Session begins, and the first card is shown.\n   - Extensions: None\n   - Trigger: User action\n\n2. **View Random Card**\n   - Actor: User\n   - Preconditions: Session is active.\n   - Main Flow: System displays a random card.\n   - Postconditions: Card is visible to the user.\n   - Extensions: None\n   - Trigger: Session start or next card action\n\n3. **Submit Card G", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Full Stack Invoice app", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FFull%20Stack%20Invoice%20app.one%7Ce5cfbfc6-4766-403e-bcb1-9c1adcf090d0%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C36783163-0834-409d-8b83-e89304686ed3%2F%29", "section_group": "Applications"}}, {"text": "on is active.\n   - Main Flow: System displays a random card.\n   - Postconditions: Card is visible to the user.\n   - Extensions: None\n   - Trigger: Session start or next card action\n\n3. **Submit Card Guess**\n   - Actor: User\n   - Preconditions: Card is displayed.\n   - Main Flow: User enters guess and submits.\n   - Postconditions: Guess is recorded.\n   - Extensions: Input validation error\n   - Trigger: User action\n\n4. **Receive Feedback on Guess**\n   - Actor: User\n   - Preconditions: Guess submitted.\n   - Main Flow: System checks guess and displays feedback.\n   - Postconditions: User sees if guess was correct.\n   - Extensions: None\n   - Trigger: Guess submission\n\n5. **View Session Score**\n   - Actor: User\n   - Preconditions: Session completed.\n   - Main Flow: System displays score.\n   - Postconditions: User sees score.\n   - Extensions: None\n   - Trigger: Session end\n\n\n\n2025-05-15 - chatgpt model 4o-mini had trouble analyzing and got in a doom loop so I went to model 4.1, which stopped in", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Full Stack Invoice app", "page": "🧩 Use Cases and Example Code", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FFull%20Stack%20Invoice%20app.one%7Ce5cfbfc6-4766-403e-bcb1-9c1adcf090d0%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C36783163-0834-409d-8b83-e89304686ed3%2F%29", "section_group": "Applications"}}, {"text": "conditions: User sees score.\n   - Extensions: None\n   - Trigger: Session end\n\n\n\n2025-05-15 - chatgpt model 4o-mini had trouble analyzing and got in a doom loop so I went to model 4.1, which stopped in the middle and I had to tell it to finish,. Then it had a lot of trouble taking the 50 use cases and putting them in a markdown file I could download.\n\nI've had this happen before so I wasn't surprised when it happened again but in other cases creating a simple spreadsheet or things like that seemed to be pretty easy but arguably those were small files.\n\n\n\nI used V.0 to take those use cases and generate a UI.\nWow, what I got back was pretty sweet for just generating from use case descriptions", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Full Stack Invoice app", "page": "🧩 Use Cases and Example Code", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FFull%20Stack%20Invoice%20app.one%7Ce5cfbfc6-4766-403e-bcb1-9c1adcf090d0%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C36783163-0834-409d-8b83-e89304686ed3%2F%29", "section_group": "Applications"}}, {"text": "📖 Overview", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Full Stack Invoice app", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FFull%20Stack%20Invoice%20app.one%7Ce5cfbfc6-4766-403e-bcb1-9c1adcf090d0%2F%F0%9F%93%96%20Overview%7C42caa6aa-a6ae-421c-9826-50bf0df8e0b5%2F%29", "section_group": "Applications"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Full Stack Invoice app", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FFull%20Stack%20Invoice%20app.one%7Ce5cfbfc6-4766-403e-bcb1-9c1adcf090d0%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C68e3561a-8c98-4f60-b052-958d24cf37cf%2F%29", "section_group": "Applications"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Full Stack Invoice app", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FFull%20Stack%20Invoice%20app.one%7Ce5cfbfc6-4766-403e-bcb1-9c1adcf090d0%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cd2f338c3-a4b7-465b-9247-9b5df489868b%2F%29", "section_group": "Applications"}}, {"text": "Reverse Engineering: [Codebase --> Use-cases --> UI]\n\n\n\n\n\n\n\nZip the codebase using this command\nzip -r health-plan-pro.zip . -x \"node_modules/*\" \"*/node_modules/*\" \".next/*\" \"coverage/*\" \".git/*\"\n\n\n\n\n\n\nChatGPT 4.1 Prompt for generating use-cases\nI’m providing you with a zipped codebase (excluding node_modules). Your task is to analyze the entire codebase and reverse engineer a high-quality list of functional and improvement-oriented use cases that a Product Manager (PM) can use to understand and iterate on this system.\nPlease follow this exact instruction set:\n✅ TASKS\n\nReverse Engineer Functional Use Cases:\n\nIdentify core product flows and functionalities implemented in the current codebase.\nFor each, write a functional use case from the perspective of the end-user or system actor.\nFocus on meaningful interactions, not just low-level actions (e.g., “Submit Card Guess” not “Click Button”).\n\n\nPropose Improvement Use Cases:\n\nAnalyze the current capabilities and limitations.\nSuggest realis", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2FReverse%20Engineering%20%5BCodebase%20--%3E%20Use-%7Cee43b41a-0226-404f-b27b-6c21f5ea0fbd%2F%29", "section_group": "Applications"}}, {"text": "n meaningful interactions, not just low-level actions (e.g., “Submit Card Guess” not “Click Button”).\n\n\nPropose Improvement Use Cases:\n\nAnalyze the current capabilities and limitations.\nSuggest realistic, high-impact feature improvements or refinements.\nThink like a PM: What would make this product more useful, scalable, intuitive, or intelligent?\n\n\nStructure the Output in Markdown:\n\nUse a consistent template for each use case:\n\n\n\n\n### [Use Case Title]\n- **Actor:** [User / Admin / System / etc.]\n- **Preconditions:** [State before action]\n- **Main Flow:** [What happens step-by-step]\n- **Postconditions:** [State after action]\n- **Extensions:** [Errors, edge cases, or optional flows]\n- **Trigger:** [What initiates this use case]\n\nOrganize Output as Follows:\n\nFirst: ### Functional Use Cases\nThen: ### Improvement Use Cases\nNumber each use case sequentially.\n\n\nQuality over Quantity:\n\nYou may generate fewer than 50 use cases in each category if that's what quality demands.\nFocus on relevance,", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2FReverse%20Engineering%20%5BCodebase%20--%3E%20Use-%7Cee43b41a-0226-404f-b27b-6c21f5ea0fbd%2F%29", "section_group": "Applications"}}, {"text": "en: ### Improvement Use Cases\nNumber each use case sequentially.\n\n\nQuality over Quantity:\n\nYou may generate fewer than 50 use cases in each category if that's what quality demands.\nFocus on relevance, clarity, completeness, and strategic value.\n\n\nAvoid Assumptions Beyond Codebase:\n\nBase your use cases strictly on what is implemented or implied in the code.\nDo not fabricate imaginary features unless you label them as future improvements.\n\n\n\n\nGoal: Help a PM understand what this system currently does and what it should do next to increase its product value.\nPlease return the final result as a clean, properly formatted Markdown document.\n\n\nValidate the generated use-cases with another LLM <Claude Sonnet 3.7>\n\nYou are an expert software requirements analyst and technical writer. Given the following use case, please: \nValidate Completeness: \nCheck that all standard use case components (Actor, Preconditions, Main Flow, Postconditions, Extensions, Trigger) are present and sufficiently detaile", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2FReverse%20Engineering%20%5BCodebase%20--%3E%20Use-%7Cee43b41a-0226-404f-b27b-6c21f5ea0fbd%2F%29", "section_group": "Applications"}}, {"text": "owing use case, please: \nValidate Completeness: \nCheck that all standard use case components (Actor, Preconditions, Main Flow, Postconditions, Extensions, Trigger) are present and sufficiently detailed. \nAssess Clarity and Unambiguity: \nEnsure each step and term is clear, specific, and understandable to both technical and non-technical stakeholders. Rewrite any ambiguous or unclear steps. \nCheck Technical Feasibility and Consistency: \nIdentify any steps that may not be realistic or implementable in typical software systems. Flag logical inconsistencies and suggest corrections to align with practical implementation. \nDetect Redundancy or Overlap: \nIf a list of existing use cases is provided, compare for redundancy or significant overlap. Suggest merging, splitting, or removal if needed. \nStandardize Format: \nEnsure the use case strictly follows the provided template. Standardize headings, bullet points, and terminology. Fill in any missing sections if possible. \nEnhance for Best Practic", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2FReverse%20Engineering%20%5BCodebase%20--%3E%20Use-%7Cee43b41a-0226-404f-b27b-6c21f5ea0fbd%2F%29", "section_group": "Applications"}}, {"text": "andardize Format: \nEnsure the use case strictly follows the provided template. Standardize headings, bullet points, and terminology. Fill in any missing sections if possible. \nEnhance for Best Practices: \nSuggest any enhancements to maximize user and business value, address edge cases, or improve the overall quality. \nProvide a Revised Version, if needed: \nOutput a fully revised, improved, and complete version of the use case in the same structured format. \n \nUse Case to Review: \n<Use-cases from .md file>\n\n\n✅ V0 Prompt: UI Blueprint Generation from Use Cases\n\nYou are a senior UX designer and front-end architect.\n\nI will give you a Markdown (.md) file containing a structured list of functional use cases. Your task is to read and understand the use cases, and generate a first-draft UI blueprint that can later be refined with design tools.\n \nEach use case follows this format:\n \n### Use Case [ID]: [Title]\n- **Actor:** [User/System/etc.]\n- **Preconditions:** [What must be true before it sta", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 4, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2FReverse%20Engineering%20%5BCodebase%20--%3E%20Use-%7Cee43b41a-0226-404f-b27b-6c21f5ea0fbd%2F%29", "section_group": "Applications"}}, {"text": "int that can later be refined with design tools.\n \nEach use case follows this format:\n \n### Use Case [ID]: [Title]\n- **Actor:** [User/System/etc.]\n- **Preconditions:** [What must be true before it starts]\n- **Main Flow:** [Primary steps]\n- **Postconditions:** [What happens after]\n- **Extensions:** [Alternate paths or failures]\n- **Trigger:** [What initiates it]\n \n---\n \n**Your task:**\n \n1. **Extract key UI elements** (pages, views, forms, buttons, modals, inputs, cards, tables, etc.) implied by the use cases.\n2. **Map each use case to a screen, widget, or interaction flow.**\n3. **Group use cases by screen or module**, if applicable.\n4. **List all screens/components** with:\n   - Name of the screen/component\n   - Purpose (based on use case)\n   - Elements/Controls needed\n   - Key interactions (click, select, hover, etc.)\n   - State transitions (e.g., loading, success, error)\n5. **Summarize the navigation flow** between screens/pages based on triggers and user flows.\n6. (Optional for later", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 5, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2FReverse%20Engineering%20%5BCodebase%20--%3E%20Use-%7Cee43b41a-0226-404f-b27b-6c21f5ea0fbd%2F%29", "section_group": "Applications"}}, {"text": "(click, select, hover, etc.)\n   - State transitions (e.g., loading, success, error)\n5. **Summarize the navigation flow** between screens/pages based on triggers and user flows.\n6. (Optional for later versions) Suggest responsive behavior for mobile and desktop.\n \nReturn the result as a structured document (Markdown or JSON), not just freeform text.\n \nDo not assume any existing UI — base everything purely on the use cases.\n \n---\n \nI will now upload the Markdown file.\n\n\n✅ V0 Prompt: UI Code/Component Generation from Blueprint Markdown\n\nYou are a front-end engineer with deep experience in React, TailwindCSS, and UI architecture.\n \nYou will be given a Markdown file that contains a structured UI blueprint. This blueprint is derived from functional use cases and includes:\n \n- A list of screens or views\n- Components per screen\n- Purpose and key interactions\n- Required inputs, buttons, and UI elements\n- Navigation logic and state transitions\n \n---\n \n**Your task:**\n \n1. Read the blueprint caref", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 6, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2FReverse%20Engineering%20%5BCodebase%20--%3E%20Use-%7Cee43b41a-0226-404f-b27b-6c21f5ea0fbd%2F%29", "section_group": "Applications"}}, {"text": "r views\n- Components per screen\n- Purpose and key interactions\n- Required inputs, buttons, and UI elements\n- Navigation logic and state transitions\n \n---\n \n**Your task:**\n \n1. Read the blueprint carefully.\n2. For each screen:\n   - Generate a React functional component using TailwindCSS.\n   - Include all described UI elements (forms, buttons, cards, tables, etc.)\n   - Implement interaction logic like state changes, conditional rendering, or basic navigation logic (e.g., between components via props/state).\n3. Ensure components are modular and follow a clean structure.\n4. Use placeholder data and stubs where dynamic logic or APIs are mentioned.\n5. Include minimal boilerplate (assume `create-react-app` or similar setup).\n6. Return each screen/component as a separate code block with a descriptive comment at the top.\n \n---\n \n**Constraints:**\n \n- Do not add any new UI elements not mentioned in the blueprint.\n- Do not use third-party UI libraries unless explicitly stated.\n- Focus on UI layout", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 7, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2FReverse%20Engineering%20%5BCodebase%20--%3E%20Use-%7Cee43b41a-0226-404f-b27b-6c21f5ea0fbd%2F%29", "section_group": "Applications"}}, {"text": "ive comment at the top.\n \n---\n \n**Constraints:**\n \n- Do not add any new UI elements not mentioned in the blueprint.\n- Do not use third-party UI libraries unless explicitly stated.\n- Focus on UI layout, state transitions, and reactivity — not backend logic.\n \n---\n \nI will now share the Markdown blueprint.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 8, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2FReverse%20Engineering%20%5BCodebase%20--%3E%20Use-%7Cee43b41a-0226-404f-b27b-6c21f5ea0fbd%2F%29", "section_group": "Applications"}}, {"text": "CI/CD Pipeline", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "CI/CD Pipeline", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2FCI%5C%2FCD%20Pipeline%7C1a81134d-3479-45c1-91f9-39a955d5f934%2F%29", "section_group": "Applications"}}, {"text": "SendGrid Configuration\n\n\n\n\n\nStep 1: Create a SendGrid Account\n1. Go to SendGrid's website\n2. Click the \"Start for Free\" button\n3. Fill out the registration form with your:\n - Email address\n - Name\n - Password\n4. Accept the terms of service and click \"Create Account\"\n5. Complete the email verification process by clicking the link sent to your email\n\nStep 2: Complete Account Setup\n1. After verifying your email, log in to your SendGrid account\n2. You'll be asked to complete your profile:\n - Enter your company name and website\n - Select \"Developer\" or appropriate role\n - Choose your email sending volume (select the smallest option for the free tier)\n - Explain how you plan to use SendGrid (e.g., \"Sending transactional emails for user notifications\")\n3. Click \"Continue\" to proceed\n\nStep 3: Verify a Sender Identity\nFor the free tier, you need to verify a sender email address:\n1. In the SendGrid dashboard, navigate to \"Settings\" in the left sidebar\n2. Click on \"Sender Authentication\"\n3. Under", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "SendGrid Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2FSendGrid%20Configuration%7C0873144c-f111-47a4-9412-9a9d98c3d6cd%2F%29", "section_group": "Applications"}}, {"text": "fy a Sender Identity\nFor the free tier, you need to verify a sender email address:\n1. In the SendGrid dashboard, navigate to \"Settings\" in the left sidebar\n2. Click on \"Sender Authentication\"\n3. Under \"Single Sender Verification\", click \"Verify a Single Sender\"\n4. Fill out the form with:\n - Sender name (e.g., \"Health Plan Pro\")\n - From email address (the email you want notifications to come from)\n - Reply-to email address (typically the same as the from address)\n - Company name\n - Company address\n - City, state, zip code, and country\n5. Click \"Create\" to submit\n6. SendGrid will send a verification email to the address you provided\n7. Open the email and click the verification link\n8. The sender identity will show as \"Verified\" in your SendGrid dashboard\n\nStep 4: Create an API Key\n1. In the left sidebar, go to \"Settings\" > \"API Keys\"\n2. Click \"Create API Key\"\n3. Enter a name for your key (e.g., \"Health Plan Pro Email Notifications\")\n4. Select \"Restricted Access\" and enable the following", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "SendGrid Configuration", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2FSendGrid%20Configuration%7C0873144c-f111-47a4-9412-9a9d98c3d6cd%2F%29", "section_group": "Applications"}}, {"text": "left sidebar, go to \"Settings\" > \"API Keys\"\n2. Click \"Create API Key\"\n3. Enter a name for your key (e.g., \"Health Plan Pro Email Notifications\")\n4. Select \"Restricted Access\" and enable the following permissions:\n - Mail Send: Full Access\n5. Click \"Create & View\"\n6. **Important**: Copy the displayed API key immediately and save it securely. You won't be able to view it again!\n\nStep 5: Add the API Key to Your Application\nAdd the SendGrid API key to your `.env` file:\n```\nSENDGRID_API_KEY=your_copied_api_key\nEMAIL_FROM=<EMAIL>\n```\n\n\n\nTroubleshooting\n\nEmail Not Received\n1. Check your spam folder\n2. Verify that the sender email is properly verified in SendGrid\n3. Ensure the API key has Mail Send permissions\n4. Check application logs for any errors\n5. Verify that the FROM email in your .env file matches the verified sender identity\n\nRate Limit Exceeded\nThe free tier has a limit of 100 emails per day. If you exceed this:\n1. Monitor your email sending volume\n2. Consider", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "SendGrid Configuration", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2FSendGrid%20Configuration%7C0873144c-f111-47a4-9412-9a9d98c3d6cd%2F%29", "section_group": "Applications"}}, {"text": "email in your .env file matches the verified sender identity\n\nRate Limit Exceeded\nThe free tier has a limit of 100 emails per day. If you exceed this:\n1. Monitor your email sending volume\n2. Consider upgrading to a paid plan if needed\n3. Implement rate limiting in your application code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "SendGrid Configuration", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2FSendGrid%20Configuration%7C0873144c-f111-47a4-9412-9a9d98c3d6cd%2F%29", "section_group": "Applications"}}, {"text": "🔧 Setup and Configuration\n\n\n\n\n\nPrerequisites\n\n\n\nNode.js\nv18 or newer recommended for App Router\n\n\n\n\nnpm or yarn\nPackage manager for Node.js\n\n\n\n\nPostgreSQL\nv12 or newer running locally or accessible\n\n\n\n\nGit\nFor cloning the repository\n\n\n\n\n\nBasic Setup\n\n1. **Clone the Repository** \n```bash \ngit clone https://github.com/creospan-inc/health-plan-pro-nextjs.git\ncd health-plan-pro-nextjs \n``` \n2. **Install Dependencies** \n```bash \nnpm install \n# or \nyarn install \n``` \n3. **Set Up Environment Variables** \n- Copy `.env.example` to `.env` \n- Update the required variables\n\nDatabase Configuration\n\n1. **Update Environment Variables** \n- Add your PostgreSQL connection string to `.env`: \n``` \nDATABASE_URL=\"postgresql://USERNAME:PASSWORD@HOST:PORT/DATABASE_NAME?schema=public\" \n``` \n(Replace `USERNAME`, `PASSWORD`, `HOST`, `PORT`, `DATABASE_NAME` with your values)\n\n2. **Run Database Migrations** \n```bash \nnpx prisma migrate dev --name init \n``` \n\n3. **Generate Prisma Client** \n```bash \nnpx prisma gener", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C0c7f217a-5a25-cd42-9ff0-eb38a394a002%2F%29", "section_group": "Applications"}}, {"text": "ASSWORD`, `HOST`, `PORT`, `DATABASE_NAME` with your values)\n\n2. **Run Database Migrations** \n```bash \nnpx prisma migrate dev --name init \n``` \n\n3. **Generate Prisma Client** \n```bash \nnpx prisma generate \n``` \n\n4. **Seed the Database (Optional but Recommended)** \n```bash \nnpx prisma db seed \n``` \n\nAuthentication Setup \n(Currently, this is taken care of in the application, but if you need a new Clerk API key, follow these steps)\n\nThe application uses Clerk for authentication. \n\n1. **Create a Clerk Account** \n- Go to clerk.com and sign up \n- Create a new application \n\n2. **Configure Clerk Settings** \n- Set up sign-in/sign-up methods \n- Configure redirect URLs (for local development, use `http://localhost:3000`) \n\n3. **Get API Keys** \n- From your Clerk dashboard, find your API keys \n- Add them to your `.env` file: \n``` \nNEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_... \nCLERK_SECRET_KEY=sk_test_... \n``` \n\n(Optional Feature) Email Notifications\nIf you want to enable email notifications (e.g.,", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "🔧 Setup and Configuration", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C0c7f217a-5a25-cd42-9ff0-eb38a394a002%2F%29", "section_group": "Applications"}}, {"text": "m to your `.env` file: \n``` \nNEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_... \nCLERK_SECRET_KEY=sk_test_... \n``` \n\n(Optional Feature) Email Notifications\nIf you want to enable email notifications (e.g., for plan selection confirmations): \n **Set Up SendGrid** \n- Follow the detailed guide given in the SendGrid Configuration page\n- Add the following to your `.env` file: \n``` \nSENDGRID_API_KEY=your-sendgrid-api-key \nEMAIL_FROM=<EMAIL> \n```", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "🔧 Setup and Configuration", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C0c7f217a-5a25-cd42-9ff0-eb38a394a002%2F%29", "section_group": "Applications"}}, {"text": "Upstash Configuration [Soon to be replaced with GCP services]", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Upstash Configuration [Soon to be replaced with GCP services] ", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2FUpstash%20Configuration%20%5BSoon%20to%20be%20replaced%20with%20GCP%20services%5D%7C34e3af7d-0bc9-4a1f-a0f7-0e67aa65d095%2F%29", "section_group": "Applications"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\nDescription:\nHealth Plan Pro is a Next.js web application using the App Router architecture for displaying, managing, and comparing healthcare plans with advanced cost simulation capabilities. It provides users with tools to browse health plans, simulate costs based on their specific medical needs, and receive personalized plan recommendations.\n\nWhen to Use:\n\nWhen users need to compare multiple healthcare plans side-by-side\nWhen individuals want to estimate their potential healthcare costs under different plans\nWhen users need personalized healthcare plan recommendations based on their specific needs\nWhen organizations want to provide a comprehensive healthcare plan selection platform\n\n\nBusiness Value: \n\nSimplifies the complex healthcare plan selection process for users\nReduces customer support needs through self-service tools\nIncreases user satisfaction through personalized recommendations\nProvides data-driven insights into user preferences and needs\n\n\nKey Terms & C", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2F%F0%9F%93%96%20Overview%7C00e251c8-8352-2b46-a16c-ba37cf22a102%2F%29", "section_group": "Applications"}}, {"text": "Reduces customer support needs through self-service tools\nIncreases user satisfaction through personalized recommendations\nProvides data-driven insights into user preferences and needs\n\n\nKey Terms & Concepts:", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "📖 Overview", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2F%F0%9F%93%96%20Overview%7C00e251c8-8352-2b46-a16c-ba37cf22a102%2F%29", "section_group": "Applications"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cc08011f8-fcb6-5640-bd71-c88272f4417e%2F%29", "section_group": "Applications"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Ca0ec6fa1-544b-364a-87a1-ef359bb2219d%2F%29", "section_group": "Applications"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FHealth%20Plans%20App.one%7C4a30001a-39c1-4cf3-9075-b33c3e882aea%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C724fbbd4-f3bb-6841-99a6-b0c72c10af29%2F%29", "section_group": "Applications"}}, {"text": "📖 Overview", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RFP Navigator", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FRFP%20Navigator.one%7Cbd1476c8-5ccc-9746-94bd-9853122d5e27%2F%F0%9F%93%96%20Overview%7Cab31cf52-1dfc-764a-8b37-e3a93b807fd1%2F%29", "section_group": "Applications"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RFP Navigator", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FRFP%20Navigator.one%7Cbd1476c8-5ccc-9746-94bd-9853122d5e27%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cb950b0cc-e756-5b4e-a553-f499b8fba4fa%2F%29", "section_group": "Applications"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RFP Navigator", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FRFP%20Navigator.one%7Cbd1476c8-5ccc-9746-94bd-9853122d5e27%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C87772dd8-94c4-8844-8039-49fee1f329d2%2F%29", "section_group": "Applications"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RFP Navigator", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FRFP%20Navigator.one%7Cbd1476c8-5ccc-9746-94bd-9853122d5e27%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Ca8d439f0-1377-2045-a495-091de6dffab9%2F%29", "section_group": "Applications"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RFP Navigator", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Applications%2FRFP%20Navigator.one%7Cbd1476c8-5ccc-9746-94bd-9853122d5e27%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cda5fa8a4-e6e6-9244-82a2-907897fb206d%2F%29", "section_group": "Applications"}}, {"text": "Fabric AI Patterns\n\n\n\n\n\nAbout\nfabric is an open-source framework for augmenting humans using AI. It provides a modular framework for solving specific problems using a crowdsourced set of AI prompts that can be used anywhere.\n\nIt can be run from the command line and instructions are in the github link below or you can just copy the <PERSON><PERSON>s folder for open source hundreds of prompts.\n\nGithub link: https://github.com/danielmiessler/fabric/tree/main\n\n\nThe system.md file in each folder has output like this.\nExtract_Wisdom:  Put this first then add your content, like article copy or youtube transcript below the prompt:\n\n# IDENTITY and PURPOSE\nYou extract surprising, insightful, and interesting information from text content. You are interested in insights related to the purpose and meaning of life, human flourishing, the role of technology in the future of humanity, artificial intelligence and its affect on humans, memes, learning, reading, books, continuous improvement, and similar topics.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "<PERSON><PERSON><PERSON>", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Prompts%2FGen%20AI%20Prompts.one%7C0b7652fe-146a-4fbc-91e2-497766133af3%2FFabric%20AI%20Patterns%7C4caaeccc-b812-482a-8cea-2bab0602cf25%2F%29", "section_group": "Gen AI Prompts"}}, {"text": "fe, human flourishing, the role of technology in the future of humanity, artificial intelligence and its affect on humans, memes, learning, reading, books, continuous improvement, and similar topics.\n\nTake a step back and think step-by-step about how to achieve the best possible results by following the steps below.\n# STEPS\n- Extract a summary of the content in 25 words, including who is presenting and the content being discussed into a section called SUMMARY.\n- Extract 20 to 50 of the most surprising, insightful, and/or interesting ideas from the input in a section called IDEAS:. If there are less than 50 then collect all of them. Make sure you extract at least 20.\n- Extract 10 to 20 of the best insights from the input and from a combination of the raw input and the IDEAS above into a section called INSIGHTS. These INSIGHTS should be fewer, more refined, more insightful, and more abstracted versions of the best ideas in the content. \n- Extract 15 to 30 of the most surprising, insightf", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "<PERSON><PERSON><PERSON>", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Prompts%2FGen%20AI%20Prompts.one%7C0b7652fe-146a-4fbc-91e2-497766133af3%2FFabric%20AI%20Patterns%7C4caaeccc-b812-482a-8cea-2bab0602cf25%2F%29", "section_group": "Gen AI Prompts"}}, {"text": "tion called INSIGHTS. These INSIGHTS should be fewer, more refined, more insightful, and more abstracted versions of the best ideas in the content. \n- Extract 15 to 30 of the most surprising, insightful, and/or interesting quotes from the input into a section called QUOTES:. Use the exact quote text from the input. Include the name of the speaker of the quote at the end.\n- Extract 15 to 30 of the most practical and useful personal habits of the speakers, or mentioned by the speakers, in the content into a section called HABITS. Examples include but aren't limited to: sleep schedule, reading habits, things they always do, things they always avoid, productivity tips, diet, exercise, etc.\n- Extract 15 to 30 of the most surprising, insightful, and/or interesting valid facts about the greater world that were mentioned in the content into a section called FACTS:.\n- Extract all mentions of writing, art, tools, projects and other sources of inspiration mentioned by the speakers into a section", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "<PERSON><PERSON><PERSON>", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Prompts%2FGen%20AI%20Prompts.one%7C0b7652fe-146a-4fbc-91e2-497766133af3%2FFabric%20AI%20Patterns%7C4caaeccc-b812-482a-8cea-2bab0602cf25%2F%29", "section_group": "Gen AI Prompts"}}, {"text": "orld that were mentioned in the content into a section called FACTS:.\n- Extract all mentions of writing, art, tools, projects and other sources of inspiration mentioned by the speakers into a section called REFERENCES. This should include any and all references to something that the speaker mentioned.\n- Extract the most potent takeaway and recommendation into a section called ONE-SENTENCE TAKEAWAY. This should be a 15-word sentence that captures the most important essence of the content.\n- Extract the 15 to 30 of the most surprising, insightful, and/or interesting recommendations that can be collected from the content into a section called RECOMMENDATIONS.\n# OUTPUT INSTRUCTIONS\n- Only output Markdown.\n - Write the IDEAS bullets as exactly 16 words.\n- Write the RECOMMENDATIONS bullets as exactly 16 words.\n- Write the HABITS bullets as exactly 16 words.\n- Write the FACTS bullets as exactly 16 words.\n- Write the INSIGHTS bullets as exactly 16 words.\n- Extract at least 25 IDEAS from the co", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "<PERSON><PERSON><PERSON>", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Prompts%2FGen%20AI%20Prompts.one%7C0b7652fe-146a-4fbc-91e2-497766133af3%2FFabric%20AI%20Patterns%7C4caaeccc-b812-482a-8cea-2bab0602cf25%2F%29", "section_group": "Gen AI Prompts"}}, {"text": "actly 16 words.\n- Write the HABITS bullets as exactly 16 words.\n- Write the FACTS bullets as exactly 16 words.\n- Write the INSIGHTS bullets as exactly 16 words.\n- Extract at least 25 IDEAS from the content.\n- Extract at least 10 INSIGHTS from the content.\n- Extract at least 20 items for the other output sections.\n- Do not give warnings or notes; only output the requested sections.\n- You use bulleted lists for output, not numbered lists.\n- Do not repeat ideas, insights, quotes, habits, facts, or references.\n- Do not start items with the same opening words.\n- Ensure you follow ALL these instructions when creating your output.\n# INPUT:", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "<PERSON><PERSON><PERSON>", "chunk_index": 4, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Prompts%2FGen%20AI%20Prompts.one%7C0b7652fe-146a-4fbc-91e2-497766133af3%2FFabric%20AI%20Patterns%7C4caaeccc-b812-482a-8cea-2bab0602cf25%2F%29", "section_group": "Gen AI Prompts"}}, {"text": "📖 Overview\n\n\n\n\n\nThis section is meant to compile all the AI prompts that we have used and are out there that we can customize for our specific needs.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Prompts%2FGen%20AI%20Prompts.one%7C0b7652fe-146a-4fbc-91e2-497766133af3%2F%F0%9F%93%96%20Overview%7C6f513538-4101-4144-8fc5-cc2ffbfe99e8%2F%29", "section_group": "Gen AI Prompts"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Prompts%2FGen%20AI%20Prompts.one%7C0b7652fe-146a-4fbc-91e2-497766133af3%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C26e77529-4bae-3a49-abe9-a31e302bae36%2F%29", "section_group": "Gen AI Prompts"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Prompts%2FGen%20AI%20Prompts.one%7C0b7652fe-146a-4fbc-91e2-497766133af3%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Cdb83cb0a-6d4f-1a45-acee-99cc3d6fa2a9%2F%29", "section_group": "Gen AI Prompts"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Prompts%2FGen%20AI%20Prompts.one%7C0b7652fe-146a-4fbc-91e2-497766133af3%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cce8f5362-57dd-8d44-a143-ee5bab2b8298%2F%29", "section_group": "Gen AI Prompts"}}, {"text": "📜 Known Issues and FAQs", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "📜 Known Issues and FAQs", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Prompts%2FGen%20AI%20Prompts.one%7C0b7652fe-146a-4fbc-91e2-497766133af3%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQs%7C91f67ffd-a09a-5949-aae6-2c0b4d2b97fc%2F%29", "section_group": "Gen AI Prompts"}}, {"text": "Full AI Prompt Database - Jeff Su\n\n\n\n\n\nThis file has many different disciplines and is sortable and searchable.  It was created by a Google Product Manager named <PERSON>.\n\nAI prompt database - Jeff Su.xlsx", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "Full AI Prompt Database - <PERSON>", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Prompts%2FGen%20AI%20Prompts.one%7C0b7652fe-146a-4fbc-91e2-497766133af3%2FFull%20AI%20Prompt%20Database%20-%20Jeff%20Su%7C706c1a51-0ec5-42b3-9faa-7fb797cde651%2F%29", "section_group": "Gen AI Prompts"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Abacus.ai", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FAbacus.ai.one%7C65761382-b16b-3a4a-847f-8a5033d2d4ad%2F%F0%9F%93%96%20Overview%7C33140e1f-36d3-d544-9d42-9a3a93cb209e%2F%29", "section_group": "Gen AI Tools"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Abacus.ai", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FAbacus.ai.one%7C65761382-b16b-3a4a-847f-8a5033d2d4ad%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C73b0141c-66c0-7446-a0f4-61c2b09dbec1%2F%29", "section_group": "Gen AI Tools"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Abacus.ai", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FAbacus.ai.one%7C65761382-b16b-3a4a-847f-8a5033d2d4ad%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cc54958bb-2b1c-2e4d-b452-ba48ad6e7cbe%2F%29", "section_group": "Gen AI Tools"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Abacus.ai", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FAbacus.ai.one%7C65761382-b16b-3a4a-847f-8a5033d2d4ad%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Cfbfec724-1238-8844-b879-20fd7490c661%2F%29", "section_group": "Gen AI Tools"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Abacus.ai", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FAbacus.ai.one%7C65761382-b16b-3a4a-847f-8a5033d2d4ad%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C72d1724f-7a83-cb40-84d2-1623b08e16fe%2F%29", "section_group": "Gen AI Tools"}}, {"text": "📖 Overview", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "ChatGPT", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FChatGPT.one%7Cafe33269-81ef-3545-8a89-b5f3c78a65b2%2F%F0%9F%93%96%20Overview%7C862b1d45-771a-bb42-a569-6dcbb5ad54b3%2F%29", "section_group": "Gen AI Tools"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "ChatGPT", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FChatGPT.one%7Cafe33269-81ef-3545-8a89-b5f3c78a65b2%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cccfbced9-d148-5043-a72b-bc81d4ab1cdc%2F%29", "section_group": "Gen AI Tools"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "ChatGPT", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FChatGPT.one%7Cafe33269-81ef-3545-8a89-b5f3c78a65b2%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C00a609d3-d5b9-b444-8640-bd63f5a56b9c%2F%29", "section_group": "Gen AI Tools"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "ChatGPT", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FChatGPT.one%7Cafe33269-81ef-3545-8a89-b5f3c78a65b2%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Cdefe737a-8eb8-2842-b3fd-bddc2ceca5cd%2F%29", "section_group": "Gen AI Tools"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "ChatGPT", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FChatGPT.one%7Cafe33269-81ef-3545-8a89-b5f3c78a65b2%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C18626ae1-84c4-194a-a457-002a0c4ee575%2F%29", "section_group": "Gen AI Tools"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\nNotebookLM is an AI-powered research and note-taking tool developed by Google Labs. It assists users in organizing, summarizing, and interacting with their documents by leveraging advanced language models. Initially introduced in 2023 as “Project Tailwind,” NotebookLM has evolved to incorporate various features aimed at enhancing the research and learning experience. \n\nKey Features of NotebookLM:\n\n\nDocument Summarization: Users can upload various document formats, including PDFs, Google Docs, and websites. NotebookLM then generates concise summaries, highlights key points, and provides explanations to facilitate understanding.  \nInteractive Q&A: The tool allows users to ask questions related to their uploaded content, providing detailed answers grounded in the source material. This feature aids in deeper comprehension and efficient information retrieval.  \nAudio Overviews: Introduced in September 2024, this feature enables NotebookLM to create podcast-style audio sum", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Notebook LLM", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FNotebook%20LLM.one%7Ce11da49f-2c5d-bd4e-895a-496aa632f14c%2F%F0%9F%93%96%20Overview%7C9ba87da2-c946-1445-b0e9-68101fe871de%2F%29", "section_group": "Gen AI Tools"}}, {"text": "erial. This feature aids in deeper comprehension and efficient information retrieval.  \nAudio Overviews: Introduced in September 2024, this feature enables NotebookLM to create podcast-style audio summaries of documents. It offers an alternative medium for users to engage with their content audibly.  \nDiscover Sources: Launched in April 2025, this functionality allows users to describe a topic of interest, and NotebookLM autonomously finds and summarizes relevant web sources. Users can then add these curated sources to their notebooks for further exploration.  \n\n\nNotebookLM is designed to serve as a virtual research assistant, aiming to streamline the process of understanding complex information by synthesizing content and generating insights based on user-provided documents.  \n\nFor more detailed information and to explore NotebookLM further, you can visit the official Google page:", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Notebook LLM", "page": "📖 Overview", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FNotebook%20LLM.one%7Ce11da49f-2c5d-bd4e-895a-496aa632f14c%2F%F0%9F%93%96%20Overview%7C9ba87da2-c946-1445-b0e9-68101fe871de%2F%29", "section_group": "Gen AI Tools"}}, {"text": "etailed information and to explore NotebookLM further, you can visit the official Google page:", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Notebook LLM", "page": "📖 Overview", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FNotebook%20LLM.one%7Ce11da49f-2c5d-bd4e-895a-496aa632f14c%2F%F0%9F%93%96%20Overview%7C9ba87da2-c946-1445-b0e9-68101fe871de%2F%29", "section_group": "Gen AI Tools"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Notebook LLM", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FNotebook%20LLM.one%7Ce11da49f-2c5d-bd4e-895a-496aa632f14c%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cf8926725-6a5c-6a4a-b5b1-514c05173a1a%2F%29", "section_group": "Gen AI Tools"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Notebook LLM", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FNotebook%20LLM.one%7Ce11da49f-2c5d-bd4e-895a-496aa632f14c%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C802b4a9f-528f-bc4d-b546-706632870a4b%2F%29", "section_group": "Gen AI Tools"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Notebook LLM", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FNotebook%20LLM.one%7Ce11da49f-2c5d-bd4e-895a-496aa632f14c%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C66b9fcb3-c73e-ce42-867d-6567631a24ab%2F%29", "section_group": "Gen AI Tools"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Notebook LLM", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FNotebook%20LLM.one%7Ce11da49f-2c5d-bd4e-895a-496aa632f14c%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C284120cd-1321-ad4e-9e1a-dc561bf0e699%2F%29", "section_group": "Gen AI Tools"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Open-WebUi", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FOpen-WebUi.one%7C194fa658-e5f9-614e-924e-d184f2058b69%2F%F0%9F%93%96%20Overview%7C2f9f2887-fb77-ad4c-b1b8-81188a07dbe1%2F%29", "section_group": "Gen AI Tools"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Open-WebUi", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FOpen-WebUi.one%7C194fa658-e5f9-614e-924e-d184f2058b69%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C24104bf6-d0f1-ec42-8f92-26db9c0f3ec0%2F%29", "section_group": "Gen AI Tools"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Open-WebUi", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FOpen-WebUi.one%7C194fa658-e5f9-614e-924e-d184f2058b69%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C73ac0740-ff30-2748-85d0-b2b456ff5dae%2F%29", "section_group": "Gen AI Tools"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Open-WebUi", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FOpen-WebUi.one%7C194fa658-e5f9-614e-924e-d184f2058b69%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C64512045-eff4-f449-9fbc-60f9d2ea07b4%2F%29", "section_group": "Gen AI Tools"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Open-WebUi", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Gen%20AI%20Tools%2FOpen-WebUi.one%7C194fa658-e5f9-614e-924e-d184f2058b69%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C48a27ac6-27b5-df4d-9878-c66224565e1f%2F%29", "section_group": "Gen AI Tools"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CodeSandBox.io", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FCodeSandBox.io.one%7C1502f165-4364-0649-a7da-cc5b3446d4b4%2F%F0%9F%93%96%20Overview%7C48a68a6f-27a6-4447-b220-28c563685fca%2F%29", "section_group": "IDEs"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CodeSandBox.io", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FCodeSandBox.io.one%7C1502f165-4364-0649-a7da-cc5b3446d4b4%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C5441b61b-f47f-e74e-80b8-0e70ea3f56f4%2F%29", "section_group": "IDEs"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CodeSandBox.io", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FCodeSandBox.io.one%7C1502f165-4364-0649-a7da-cc5b3446d4b4%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cc48a22ae-0e18-5b4b-816c-a08d896907cd%2F%29", "section_group": "IDEs"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CodeSandBox.io", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FCodeSandBox.io.one%7C1502f165-4364-0649-a7da-cc5b3446d4b4%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C3b2e0619-85f8-aa4d-bc87-336719f0faba%2F%29", "section_group": "IDEs"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CodeSandBox.io", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FCodeSandBox.io.one%7C1502f165-4364-0649-a7da-cc5b3446d4b4%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cddac0f03-6321-8149-bf72-fcebc7153dae%2F%29", "section_group": "IDEs"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Cursor.ai", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FCursor.ai.one%7C9d8c2bea-37fa-4e5f-9cf8-9bf01b649831%2F%F0%9F%93%96%20Overview%7Cb359cacc-bee6-f14d-91c7-bd0a41a3dd7c%2F%29", "section_group": "IDEs"}}, {"text": "📜 Known Issues and FAQ's\n\n\n\n\n\nL", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Cursor.ai", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FCursor.ai.one%7C9d8c2bea-37fa-4e5f-9cf8-9bf01b649831%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C8f1254a4-776b-6f4c-af48-98d72c9815de%2F%29", "section_group": "IDEs"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Cursor.ai", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FCursor.ai.one%7C9d8c2bea-37fa-4e5f-9cf8-9bf01b649831%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C464316a7-84d0-c248-b6f7-ba57bc23e285%2F%29", "section_group": "IDEs"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Cursor.ai", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FCursor.ai.one%7C9d8c2bea-37fa-4e5f-9cf8-9bf01b649831%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C9f7917f2-21fa-244a-be1c-942538c553f6%2F%29", "section_group": "IDEs"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Cursor.ai", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FCursor.ai.one%7C9d8c2bea-37fa-4e5f-9cf8-9bf01b649831%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C9c6a88b6-25e3-4848-8ecf-393a29f329db%2F%29", "section_group": "IDEs"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Replit.com", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FReplit.com.one%7C8c7d2e99-62e2-584f-b390-0ade71123bf6%2F%F0%9F%93%96%20Overview%7C8dd32972-bfed-3a40-9a7f-e3a396734d2f%2F%29", "section_group": "IDEs"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Replit.com", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FReplit.com.one%7C8c7d2e99-62e2-584f-b390-0ade71123bf6%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C658f60b0-e074-3440-9f3a-84105dc3e39a%2F%29", "section_group": "IDEs"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Replit.com", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FReplit.com.one%7C8c7d2e99-62e2-584f-b390-0ade71123bf6%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C84c8cd4c-c119-c648-a065-66fc29e8c652%2F%29", "section_group": "IDEs"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Replit.com", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FReplit.com.one%7C8c7d2e99-62e2-584f-b390-0ade71123bf6%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C06e9dc10-cbc0-e646-b059-b1fe87332531%2F%29", "section_group": "IDEs"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Replit.com", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FReplit.com.one%7C8c7d2e99-62e2-584f-b390-0ade71123bf6%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C6d2747d9-dbe4-e441-9973-f8475c6db81d%2F%29", "section_group": "IDEs"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FV.0.one%7C9cca653d-663e-f844-a8d2-3be879dec0b3%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C35688ba1-c999-4ad2-88c0-b1bc6542ef10%2F%29", "section_group": "IDEs"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FV.0.one%7C9cca653d-663e-f844-a8d2-3be879dec0b3%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Ca08dd2ff-a1c8-478f-870c-2cffa3393733%2F%29", "section_group": "IDEs"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FV.0.one%7C9cca653d-663e-f844-a8d2-3be879dec0b3%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C1f5e4d28-d8cf-44d1-a095-52885ccd3b63%2F%29", "section_group": "IDEs"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FV.0.one%7C9cca653d-663e-f844-a8d2-3be879dec0b3%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cd745748c-1d95-4389-9ac3-d49d9d8b5ad6%2F%29", "section_group": "IDEs"}}, {"text": "📖 Overview", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FV.0.one%7C9cca653d-663e-f844-a8d2-3be879dec0b3%2F%F0%9F%93%96%20Overview%7Cd14d35b3-0872-4f4f-b4b5-48d704c2e89d%2F%29", "section_group": "IDEs"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FV.0.one%7C9cca653d-663e-f844-a8d2-3be879dec0b3%2F%F0%9F%93%96%20Overview%7C810bbb82-d7a9-ed4c-a068-5f1cee94abef%2F%29", "section_group": "IDEs"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FV.0.one%7C9cca653d-663e-f844-a8d2-3be879dec0b3%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C149f8f4c-04b1-8f41-b2bc-749335c33098%2F%29", "section_group": "IDEs"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FV.0.one%7C9cca653d-663e-f844-a8d2-3be879dec0b3%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C94f7faa1-793f-e048-9b69-3672e2118093%2F%29", "section_group": "IDEs"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FV.0.one%7C9cca653d-663e-f844-a8d2-3be879dec0b3%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Cea7551df-8979-1048-9095-b4c60b220c1e%2F%29", "section_group": "IDEs"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FV.0.one%7C9cca653d-663e-f844-a8d2-3be879dec0b3%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C72d2b0f8-49ca-a849-9d05-9f004350d544%2F%29", "section_group": "IDEs"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Visual Studio Code", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FVisual%20Studio%20Code.one%7C1c5679f7-713a-8d42-83c1-fbdec5b17359%2F%F0%9F%93%96%20Overview%7C6ccbede9-74ce-a84c-b8dd-dddf409ec417%2F%29", "section_group": "IDEs"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Visual Studio Code", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FVisual%20Studio%20Code.one%7C1c5679f7-713a-8d42-83c1-fbdec5b17359%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cc50d0cf8-9a80-2542-b72e-d5c925c199c7%2F%29", "section_group": "IDEs"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Visual Studio Code", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FVisual%20Studio%20Code.one%7C1c5679f7-713a-8d42-83c1-fbdec5b17359%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca6e0a3b7-e9cd-a044-82b9-70f48e394021%2F%29", "section_group": "IDEs"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Visual Studio Code", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FVisual%20Studio%20Code.one%7C1c5679f7-713a-8d42-83c1-fbdec5b17359%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Cf53b62c8-f378-b04b-9c77-66e1986cda97%2F%29", "section_group": "IDEs"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Visual Studio Code", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28IDEs%2FVisual%20Studio%20Code.one%7C1c5679f7-713a-8d42-83c1-fbdec5b17359%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Ca7a5c36a-b247-3941-809e-926c19a81ad1%2F%29", "section_group": "IDEs"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Claude 3.7", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FClaude%203.7.one%7Caf2517d5-190e-0249-bc66-afbf718bbab7%2F%F0%9F%93%96%20Overview%7Cd731aa41-02be-f749-bbdf-29dfc9026e48%2F%29", "section_group": "LLM Models"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Claude 3.7", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FClaude%203.7.one%7Caf2517d5-190e-0249-bc66-afbf718bbab7%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C4ec3748d-e136-734e-a212-feac174f1fc9%2F%29", "section_group": "LLM Models"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Claude 3.7", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FClaude%203.7.one%7Caf2517d5-190e-0249-bc66-afbf718bbab7%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca4c55ee5-a073-4447-8781-466a781254bf%2F%29", "section_group": "LLM Models"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Claude 3.7", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FClaude%203.7.one%7Caf2517d5-190e-0249-bc66-afbf718bbab7%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C66e007ec-468e-3c4c-80bf-054a3663a4aa%2F%29", "section_group": "LLM Models"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Claude 3.7", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FClaude%203.7.one%7Caf2517d5-190e-0249-bc66-afbf718bbab7%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C5f2d4f00-b587-5345-b76c-c33a486d27d5%2F%29", "section_group": "LLM Models"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gemini 2.5", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FGemini%202.5.one%7Ce766d7d8-ee4f-0442-8d42-e8eb14c9f441%2F%F0%9F%93%96%20Overview%7C3f08986d-55f4-a549-9b6b-71bb7e5056e0%2F%29", "section_group": "LLM Models"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gemini 2.5", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FGemini%202.5.one%7Ce766d7d8-ee4f-0442-8d42-e8eb14c9f441%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cc003fe22-dcdb-194b-8484-ddd451e162fb%2F%29", "section_group": "LLM Models"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gemini 2.5", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FGemini%202.5.one%7Ce766d7d8-ee4f-0442-8d42-e8eb14c9f441%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C8648bb78-5fba-3844-8d7d-68e00e6544d7%2F%29", "section_group": "LLM Models"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gemini 2.5", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FGemini%202.5.one%7Ce766d7d8-ee4f-0442-8d42-e8eb14c9f441%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C93039303-9f23-324b-b3c4-b1d8724cba2f%2F%29", "section_group": "LLM Models"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gemini 2.5", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FGemini%202.5.one%7Ce766d7d8-ee4f-0442-8d42-e8eb14c9f441%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C86aad008-0d82-1242-bc99-5fff9ccfa2e9%2F%29", "section_group": "LLM Models"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FLLama%204.one%7Cd9705bf2-f645-7d44-bd1c-b15697d71880%2F%F0%9F%93%96%20Overview%7C3e6667db-4ef0-374b-ae1c-93660c27f2ef%2F%29", "section_group": "LLM Models"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FLLama%204.one%7Cd9705bf2-f645-7d44-bd1c-b15697d71880%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C2be246b4-8cb7-234b-a618-f22a0ae8468b%2F%29", "section_group": "LLM Models"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FLLama%204.one%7Cd9705bf2-f645-7d44-bd1c-b15697d71880%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C1aacaf80-821b-8b4d-b961-c8ce402e7d69%2F%29", "section_group": "LLM Models"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FLLama%204.one%7Cd9705bf2-f645-7d44-bd1c-b15697d71880%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C4091e42a-7f17-2f43-bf86-15d3f2d0c447%2F%29", "section_group": "LLM Models"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FLLama%204.one%7Cd9705bf2-f645-7d44-bd1c-b15697d71880%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C889e371e-8842-334d-97de-b916715a4bc1%2F%29", "section_group": "LLM Models"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FLLama%204.one%7Cd9705bf2-f645-7d44-bd1c-b15697d71880%2F%F0%9F%93%96%20Overview%7C5c4a2aaf-8e91-5540-8802-78115d9d23c9%2F%29", "section_group": "LLM Models"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FLLama%204.one%7Cd9705bf2-f645-7d44-bd1c-b15697d71880%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C8e8dd8fe-ea9b-7244-802a-d4f844e2cc3a%2F%29", "section_group": "LLM Models"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FLLama%204.one%7Cd9705bf2-f645-7d44-bd1c-b15697d71880%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C082ab871-9b80-6647-94db-1cccf8e87dba%2F%29", "section_group": "LLM Models"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FLLama%204.one%7Cd9705bf2-f645-7d44-bd1c-b15697d71880%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Ccb338f90-c851-5647-8457-1ec82a5f40f6%2F%29", "section_group": "LLM Models"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FLLama%204.one%7Cd9705bf2-f645-7d44-bd1c-b15697d71880%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Ca58e56a8-9b69-954b-831e-2a7e9ec47f88%2F%29", "section_group": "LLM Models"}}, {"text": "📜 Known Issues and FAQ's\n\n\n\n\n⚠️ Known Issues\n\n\nHigh Memory Usage￼Some models like llama3 or mistral require 8–16 GB of RAM. On lower-spec machines, this can lead to crashes or slow response times. Try smaller models like phi or gemma for lightweight tasks.\nModel Not Found / Registry Issues￼Running ollama pull <model> may fail if the model name is incorrect or if the registry is temporarily down. Double-check the model list at ollama.com/library.\nMac M1/M2 GPU Limitations￼Ollama supports Apple Silicon, but model execution is still CPU-bound. GPU acceleration is planned but not yet standard.\nPort Conflicts on localhost:11434￼The Ollama API uses port 11434 by default. If another service is running on that port, API calls will fail. You can’t change the port currently, but you can stop the conflicting service or container.\nNo Built-in Authentication￼The local API (localhost:11434) is open and unsecured by design. Be careful exposing it beyond localhost (e.g., through tunnels or Docker cont", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C8a48323e-34df-9740-aac0-77438114c8fc%2F%29", "section_group": "LLM Models"}}, {"text": "flicting service or container.\nNo Built-in Authentication￼The local API (localhost:11434) is open and unsecured by design. Be careful exposing it beyond localhost (e.g., through tunnels or Docker containers).\n\n\n\n\n❓ Frequently Asked Questions (FAQ)\n\nQ: Do I need an internet connection to run models?\nA: Only for the initial ollama pull. After the model is downloaded, all inference runs locally.\n\nQ: Can I use Ollama with my own data?\nA: Yes. You can pass data into a prompt or build a RAG system by retrieving content and injecting it into the prompt. Use the HTTP API for programmatic control.\n\nQ: Where are models stored?\nA: Models are stored in ~/.ollama by default. You can view them with ollama list.\n\nQ: Can I run Ollama on a remote server or Docker?\nA: Yes, but Ollama is optimized for local use. Remote deployment requires securing the exposed API and potentially customizing storage volumes.\n\nQ: How do I fine-tune or personalize a model?\nA: Use Modelfile and LoRA adapters to layer in chan", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "📜 Known Issues and FAQ's", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C8a48323e-34df-9740-aac0-77438114c8fc%2F%29", "section_group": "LLM Models"}}, {"text": "use. Remote deployment requires securing the exposed API and potentially customizing storage volumes.\n\nQ: How do I fine-tune or personalize a model?\nA: Use Modelfile and LoRA adapters to layer in changes. Full fine-tuning isn’t supported in Ollama yet, but adapter support is a good middle ground.\n\nQ: Can I use Ollama in a production app?\nA: Yes, but note that Ollama is still rapidly evolving. For production, test extensively and monitor performance—especially with memory usage and latency.\n\nQ: What models are available?\nA: You can view the full list at ollama.com/library or run ollama pull with a model name like llama3, mistral, or codellama.\n\n\nLet me know if you want this formatted for a slide, wiki page, or onboarding doc.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "📜 Known Issues and FAQ's", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C8a48323e-34df-9740-aac0-77438114c8fc%2F%29", "section_group": "LLM Models"}}, {"text": "💡 Tips Tricks and Shortcuts\n\n\n\n\n\n\n\n\nStart by always using ollama list to keep track of your downloaded models. To avoid retyping long model names, use shell autocompletion or create aliases (e.g., alias chat='ollama run llama3'). When experimenting with prompts, the --system flag in a Modelfile can define a default system message that influences tone and behavior, which is useful for custom personas or roles.\n\nFor faster startup and reduced RAM use, prefer smaller models like mistral or phi, especially when prototyping. You can also pass num_predict and temperature options in the API or Modelfile to tweak generation length and creativity on the fly. Keep stream=true in your API calls for real-time output—this gives your app more responsiveness.\n\nIf you’re building a custom workflow, use ollama create with a Modelfile to bundle your own prompt instructions, LoRA adapters, and settings into a reusable package. Want to keep models off production systems? Just mount the model directory wit", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Ce3d6cc16-1275-384f-9104-c175acb3a485%2F%29", "section_group": "LLM Models"}}, {"text": "lama create with a Modelfile to bundle your own prompt instructions, LoRA adapters, and settings into a reusable package. Want to keep models off production systems? Just mount the model directory with a symlink or point to a shared location—Ollama stores models in ~/.ollama.\n\nFinally, monitor Ollama’s background service via ps aux | grep ollama or system logs if something seems off. You can restart it manually using ollama serve or your system process manager. These small efficiencies make Ollama a powerful, flexible engine for local LLM workflows.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Ce3d6cc16-1275-384f-9104-c175acb3a485%2F%29", "section_group": "LLM Models"}}, {"text": "🧩 Use Cases and Example Code\n\n\n\n\n\n\n\n\nOllama unlocks a wide range of use cases by allowing users to run large language models entirely on their local machines. One of the most common uses is for local inference and experimentation, where developers or researchers test prompts and evaluate different models without relying on external APIs. This is especially useful for tasks like summarization, translation, question-answering, and creative writing, where fast iteration and data privacy are essential. Because Ollama runs offline, it is ideal for environments where cloud connectivity is limited or undesirable.\n\nAnother key use case is integrating AI into desktop and server-side applications. With its lightweight HTTP API, Ollama can be used to power chatbots, assistants, or content generation tools in productivity apps, knowledge bases, or developer tools. Developers can script interactions with the model, build pipelines, or connect it with other local services like databases or search en", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cfc6740af-71fe-a549-84e1-0d5427167357%2F%29", "section_group": "LLM Models"}}, {"text": "tools in productivity apps, knowledge bases, or developer tools. Developers can script interactions with the model, build pipelines, or connect it with other local services like databases or search engines to create hybrid RAG (retrieval-augmented generation) systems. Since the models are containerized and easy to swap, users can quickly test or upgrade capabilities without rewriting code.\n\nOllama is also a strong fit for educational settings and secure enterprise environments. In classrooms or training labs, Ollama allows instructors to teach NLP techniques, prompt engineering, and model fine-tuning without requiring cloud accounts or costly API tokens. In enterprises, Ollama can be used internally to process sensitive information—such as legal documents, contracts, or customer data—entirely within a secure perimeter. This aligns with data governance and compliance requirements, especially for regulated industries.\n\nFor individual professionals, content creators, and hobbyists, Ollama", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🧩 Use Cases and Example Code", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cfc6740af-71fe-a549-84e1-0d5427167357%2F%29", "section_group": "LLM Models"}}, {"text": "ely within a secure perimeter. This aligns with data governance and compliance requirements, especially for regulated industries.\n\nFor individual professionals, content creators, and hobbyists, Ollama enables creative workflows without needing expensive GPU servers or subscriptions. Writers can use it for idea generation, coding assistants can help developers autocomplete functions or explain code, and hobbyists can experiment with model behaviors or fine-tune small adapters using LoRA. Because Ollama supports community-contributed and custom models, the platform fosters a vibrant ecosystem where users can remix and adapt LLMs for their unique needs.\n\n\nPython Example of using the api:\n\nimport requests\n\nOLLAMA_API_URL = \"http://localhost:11434/api/generate\"\nmodel_name = \"llama3\"\n\ndef ask_ollama(prompt):\n    response = requests.post(\n        OLLAMA_API_URL,\n        json={\n            \"model\": model_name,\n            \"prompt\": prompt,\n            \"stream\": False  # Set to True if you want", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🧩 Use Cases and Example Code", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cfc6740af-71fe-a549-84e1-0d5427167357%2F%29", "section_group": "LLM Models"}}, {"text": "lama(prompt):\n    response = requests.post(\n        OLLAMA_API_URL,\n        json={\n            \"model\": model_name,\n            \"prompt\": prompt,\n            \"stream\": False  # Set to True if you want to stream responses\n        }\n    )\n    result = response.json()\n    return result.get(\"response\", \"\").strip()\n\n# Example usage\nquestion = \"What are the main benefits of running LLMs locally?\"\nanswer = ask_ollama(question)\nprint(\"Ollama says:\\n\", answer)\n\n\nHere are the most common and useful ollama CLI commands to manage and interact with models locally:\n\n🔧 \nModel Management Commands\n\n\nollama list￼Lists all models currently installed on your machine.\n\nollama list\n\n\nollama pull <model>￼Downloads a model from the Ollama model registry.\n\nollama pull llama3\n\n\nollama run <model>￼Starts an interactive prompt session with the model.\n\nollama run llama3\n\n\nollama create <model> -f Modelfile￼Builds a custom model from a Modelfile.\n\nollama create my-custom-model -f Modelfile\n\n\nollama push <model>￼Pus", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🧩 Use Cases and Example Code", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cfc6740af-71fe-a549-84e1-0d5427167357%2F%29", "section_group": "LLM Models"}}, {"text": "ive prompt session with the model.\n\nollama run llama3\n\n\nollama create <model> -f Modelfile￼Builds a custom model from a Modelfile.\n\nollama create my-custom-model -f Modelfile\n\n\nollama push <model>￼Pushes a local model to your Ollama account (if supported).\n\nollama push my-custom-model\n\n\nollama rm <model>￼Removes a downloaded model from your system.\n\nollama rm llama3\n\n\nollama show <model>￼Displays metadata and configuration details about a model.\n\nollama show llama3\n\n\n\n\n⚙️ \nService Commands\n\n\nollama serve￼Starts the Ollama API service (usually runs in the background automatically).\n\nollama serve\n\n\nollama version￼Shows the installed version of Ollama.\n\nollama version\n\n\nollama help￼Displays the help menu with a list of all commands.\n\nollama help", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🧩 Use Cases and Example Code", "chunk_index": 4, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cfc6740af-71fe-a549-84e1-0d5427167357%2F%29", "section_group": "LLM Models"}}, {"text": "🔧 Setup and Configuration\n\n\n\n\n\n\n\n\nSetting up Ollama is a streamlined process designed for ease of use, even for those new to running large language models locally. The first step is installing Ollama, which supports macOS, Linux, and Windows. On macOS, it can be installed via Homebrew using brew install ollama, while Linux users can use curl to download and run the installation script. Windows users typically install via WSL (Windows Subsystem for Linux) or through a native installer, depending on the release. Once installed, Ollama runs as a background service that handles model management, execution, and interaction via the terminal or API.\n\nAfter installation, configuring Ollama begins with pulling a model using a simple command like ollama pull llama3. This downloads the necessary model weights and metadata to your local machine, stored in a centralized location. The models are bundled using a container-like system, which includes not only the model itself but configuration files,", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C7f906d91-66a0-c045-8def-c5aca5d9baa3%2F%29", "section_group": "LLM Models"}}, {"text": "l weights and metadata to your local machine, stored in a centralized location. The models are bundled using a container-like system, which includes not only the model itself but configuration files, dependencies, and settings. This makes model usage consistent and portable. Users can also run ollama run llama3 to begin an interactive session with the model, which opens a command-line interface for prompting and interacting with the AI.\n\nA powerful part of Ollama’s configuration is the use of the Modelfile, which allows users to define custom behavior for models. The Modelfile includes parameters such as the system prompt, temperature, top-p, and other sampling options, as well as instructions to import LoRA adapters or apply pre-processing hooks. This enables fine-tuned control over how a model behaves during inference without having to modify core code or retrain the model. Modelfiles are especially useful for packaging custom workflows or building reusable model configurations acros", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🔧 Setup and Configuration", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C7f906d91-66a0-c045-8def-c5aca5d9baa3%2F%29", "section_group": "LLM Models"}}, {"text": "model behaves during inference without having to modify core code or retrain the model. Modelfiles are especially useful for packaging custom workflows or building reusable model configurations across projects.\n\nBeyond local configuration, Ollama can be integrated into applications through its HTTP API. By default, it exposes an endpoint at http://localhost:11434 that allows developers to send requests and receive model responses programmatically. This makes Ollama suitable for embedding AI into local tools, scripting workflows, or even building lightweight RAG systems. API keys and permissions are not needed for local access, which makes development quick and secure by design. With minimal setup and powerful configuration capabilities, Ollama stands out as a practical solution for bringing LLMs into local and private environments.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🔧 Setup and Configuration", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C7f906d91-66a0-c045-8def-c5aca5d9baa3%2F%29", "section_group": "LLM Models"}}, {"text": "ng LLMs into local and private environments.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🔧 Setup and Configuration", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C7f906d91-66a0-c045-8def-c5aca5d9baa3%2F%29", "section_group": "LLM Models"}}, {"text": "📖 Overview\n\n\n\n\n\nOllama is an open-source platform that enables users to run large language models (LLMs) directly on their local machines. Designed with privacy, control, and flexibility in mind, Ollama caters to developers, researchers, and organizations seeking to leverage AI capabilities without relying on cloud-based services. By facilitating local execution of models, Ollama ensures that data remains on the user’s device, enhancing security and reducing latency. Its compatibility with various operating systems, including macOS, Linux, and Windows, makes it accessible to a broad user base. \n\nAt the core of Ollama’s functionality is its ability to manage and run LLMs seamlessly. Users can download models from Ollama’s extensive library, which includes popular options like Llama 3.1, Mistral, and Phi 3. Once downloaded, these models can be executed locally, allowing for tasks such as text generation, summarization, and translation. Ollama employs a containerized approach, bundling mo", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%93%96%20Overview%7Ca73f287b-c182-df4c-80cc-0108adf9e8d2%2F%29", "section_group": "LLM Models"}}, {"text": "tral, and Phi 3. Once downloaded, these models can be executed locally, allowing for tasks such as text generation, summarization, and translation. Ollama employs a containerized approach, bundling model weights, configuration files, and dependencies into a cohesive package. This design simplifies the setup process and ensures consistency across different environments . \n\nA standout feature of Ollama is its “Modelfile” system, which allows users to customize models to suit specific needs. Through Modelfiles, users can define parameters like temperature settings, system prompts, and even integrate Low-Rank Adaptation (LoRA) for efficient fine-tuning. This level of customization empowers users to tailor models for particular applications without the need for extensive retraining. Additionally, Ollama’s support for various model formats, including GGUF, PyTorch, and Safetensors, enhances its versatility and ease of integration . \n\nBeyond its technical capabilities, Ollama offers practical", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "📖 Overview", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%93%96%20Overview%7Ca73f287b-c182-df4c-80cc-0108adf9e8d2%2F%29", "section_group": "LLM Models"}}, {"text": "y, Ollama’s support for various model formats, including GGUF, PyTorch, and Safetensors, enhances its versatility and ease of integration . \n\nBeyond its technical capabilities, Ollama offers practical benefits that resonate with a diverse user base. For developers and researchers, it provides a platform for experimenting with AI models in a controlled, offline environment. Educators can utilize Ollama to teach concepts related to natural language processing without the complexities of cloud infrastructure. Moreover, organizations concerned with data privacy can deploy Ollama to process sensitive information locally, mitigating risks associated with data transmission. By bridging the gap between powerful AI models and user-friendly deployment, Ollama stands as a valuable tool in the evolving landscape of artificial intelligence .", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "📖 Overview", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%93%96%20Overview%7Ca73f287b-c182-df4c-80cc-0108adf9e8d2%2F%29", "section_group": "LLM Models"}}, {"text": "g landscape of artificial intelligence .", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "📖 Overview", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FOllama.one%7Ccc674577-6848-d046-8275-8f84910fa7ab%2F%F0%9F%93%96%20Overview%7Ca73f287b-c182-df4c-80cc-0108adf9e8d2%2F%29", "section_group": "LLM Models"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FPerplexity.one%7C34f2242e-9a29-ff41-8a83-0f79388bd5c9%2F%F0%9F%93%96%20Overview%7Cc4a840dc-871e-024e-b33d-d667ad9b3d9f%2F%29", "section_group": "LLM Models"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FPerplexity.one%7C34f2242e-9a29-ff41-8a83-0f79388bd5c9%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C950d09e6-9fe2-4847-b83c-3dfbe7995654%2F%29", "section_group": "LLM Models"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FPerplexity.one%7C34f2242e-9a29-ff41-8a83-0f79388bd5c9%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C4951ebb7-c161-104b-9274-60573add6ea9%2F%29", "section_group": "LLM Models"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FPerplexity.one%7C34f2242e-9a29-ff41-8a83-0f79388bd5c9%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C794f6301-12fd-2946-8b48-9c1a50e75549%2F%29", "section_group": "LLM Models"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FPerplexity.one%7C34f2242e-9a29-ff41-8a83-0f79388bd5c9%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C1868eb96-cf16-9544-92ce-d6267f34914d%2F%29", "section_group": "LLM Models"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FPerplexity.one%7C34f2242e-9a29-ff41-8a83-0f79388bd5c9%2F%F0%9F%93%96%20Overview%7C8370ad6a-81ff-6343-a9fe-77a1dce57fad%2F%29", "section_group": "LLM Models"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FPerplexity.one%7C34f2242e-9a29-ff41-8a83-0f79388bd5c9%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cf0804ce9-e495-4143-9ef0-38f8b9965a2f%2F%29", "section_group": "LLM Models"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FPerplexity.one%7C34f2242e-9a29-ff41-8a83-0f79388bd5c9%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C2e78dcb5-bdd2-1448-b114-dc022fb9ad87%2F%29", "section_group": "LLM Models"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FPerplexity.one%7C34f2242e-9a29-ff41-8a83-0f79388bd5c9%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C8a21eb6b-5d3d-a64d-9a76-a9ecb40b2b27%2F%29", "section_group": "LLM Models"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28LLM%20Models%2FPerplexity.one%7C34f2242e-9a29-ff41-8a83-0f79388bd5c9%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C053575e1-8098-f446-add7-93d16896a35a%2F%29", "section_group": "LLM Models"}}, {"text": "📖 Overview", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Automate Selenium tests using ai", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FAutomate%20Selenium%20tests%20using%20ai.one%7C83ea966a-2282-4350-82b3-d16bd49c5f5e%2F%F0%9F%93%96%20Overview%7C47a8b64f-c7c7-4e4b-b618-3cd3d23e0bf0%2F%29", "section_group": "POCs"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Automate Selenium tests using ai", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FAutomate%20Selenium%20tests%20using%20ai.one%7C83ea966a-2282-4350-82b3-d16bd49c5f5e%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C7bfd88cc-3f6b-fd4c-9cbb-159fbe3defa0%2F%29", "section_group": "POCs"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Automate Selenium tests using ai", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FAutomate%20Selenium%20tests%20using%20ai.one%7C83ea966a-2282-4350-82b3-d16bd49c5f5e%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C04580991-7cee-3a40-b886-32f6ce090af2%2F%29", "section_group": "POCs"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Automate Selenium tests using ai", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FAutomate%20Selenium%20tests%20using%20ai.one%7C83ea966a-2282-4350-82b3-d16bd49c5f5e%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Cc0d1f85c-a34b-b141-9389-562b744fc12e%2F%29", "section_group": "POCs"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Automate Selenium tests using ai", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FAutomate%20Selenium%20tests%20using%20ai.one%7C83ea966a-2282-4350-82b3-d16bd49c5f5e%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C5fefc753-92de-8149-aac4-1ef5be956d19%2F%29", "section_group": "POCs"}}, {"text": "📖 Overview\n\n\n\n\n2025-05-16 – As a poc I was asked to look at taking a figma diagram and convert it into a next.js/react web site code.\n\nOverview\nTransforming a Figma PDF export into a fully functional Next.js/React website is a powerful workflow for designers and developers aiming to bridge the gap between design and code. This process leverages the strengths of Figma for design, the convenience of PDF exports for sharing, and the rapid prototyping capabilities of V0 to generate React components. By following this workflow, teams can accelerate the transition from static designs to interactive web applications.\nThe core idea is to use Figma as the single source of truth for UI/UX, export the design as a PDF for easy reference, and then utilize V0 to interpret and convert these designs into React code. V0, an AI-powered tool, can analyze design files and generate code that closely matches the original intent, reducing manual coding effort and potential misinterpretations. This approach i", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FFrom%20figma%20export%20create%20react%20site.one%7C20a7ebb2-7c15-43a3-b825-b08601dc3d13%2F%F0%9F%93%96%20Overview%7C053413f6-b633-4a4a-ba5c-01447de67a9f%2F%29", "section_group": "POCs"}}, {"text": "React code. V0, an AI-powered tool, can analyze design files and generate code that closely matches the original intent, reducing manual coding effort and potential misinterpretations. This approach is especially useful for teams with tight deadlines or limited front-end resources.\nOne of the main advantages of this workflow is the preservation of design fidelity. By starting with a Figma export, you ensure that the visual aspects of the website remain consistent with the designer’s vision. V0’s code generation further streamlines the process, allowing developers to focus on functionality and integration rather than pixel-perfect recreation.\nThis method is also highly collaborative. Designers can iterate in Figma, share updated PDFs, and developers can quickly regenerate or update components as needed. The workflow supports rapid prototyping, making it ideal for startups, agencies, or any team that values speed and agility in product development.\nAnother benefit is the reduction of onb", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "📖 Overview", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FFrom%20figma%20export%20create%20react%20site.one%7C20a7ebb2-7c15-43a3-b825-b08601dc3d13%2F%F0%9F%93%96%20Overview%7C053413f6-b633-4a4a-ba5c-01447de67a9f%2F%29", "section_group": "POCs"}}, {"text": "ts as needed. The workflow supports rapid prototyping, making it ideal for startups, agencies, or any team that values speed and agility in product development.\nAnother benefit is the reduction of onboarding time for new developers. With a clear, documented process and generated code that mirrors the design, new team members can quickly understand the project’s structure and visual hierarchy. This leads to better maintainability and scalability as the project grows.\nOverall, converting Figma PDF exports to Next.js/React code using V0 is a modern, efficient approach to web development. It empowers teams to move from design to deployment faster, with fewer errors and greater alignment between design and engineering.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "📖 Overview", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FFrom%20figma%20export%20create%20react%20site.one%7C20a7ebb2-7c15-43a3-b825-b08601dc3d13%2F%F0%9F%93%96%20Overview%7C053413f6-b633-4a4a-ba5c-01447de67a9f%2F%29", "section_group": "POCs"}}, {"text": "📜 Known Issues and FAQ's\n\n\n\n\nOne common issue is that V0 may not perfectly interpret complex Figma designs, especially those with intricate layer structures or custom interactions. In such cases, manual adjustments to the generated code may be necessary to achieve the desired look and feel. It’s important to review the output carefully and compare it against the original design.\nAnother challenge is handling responsive layouts. While V0 does a good job with basic responsiveness, edge cases may require additional media queries or layout tweaks in your React components. Testing across different devices and screen sizes is essential to ensure a consistent user experience.\nAsset management can also be tricky. If your Figma design references external images, fonts, or icons, make sure these assets are included in your Next.js project’s public directory and properly linked in the generated code. Missing assets can lead to broken images or inconsistent typography.\nA frequently asked question", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FFrom%20figma%20export%20create%20react%20site.one%7C20a7ebb2-7c15-43a3-b825-b08601dc3d13%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cdd624d19-3c4a-44f7-87e7-504cac96d362%2F%29", "section_group": "POCs"}}, {"text": "ets are included in your Next.js project’s public directory and properly linked in the generated code. Missing assets can lead to broken images or inconsistent typography.\nA frequently asked question is whether this workflow supports dynamic content or interactivity. While V0 generates static components based on the design, you’ll need to manually add logic for dynamic data, user input, or API integration. The generated code serves as a starting point, but further development is required for full functionality.\nSome users report issues with code quality or maintainability, especially in larger projects. To mitigate this, refactor the generated code as needed, extract reusable components, and adhere to your team’s coding standards. Regular code reviews and automated testing can help maintain a high-quality codebase.\nLastly, updates to the Figma design may require re-exporting and regenerating components, which can overwrite manual changes. To avoid losing work, document any customizatio", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "📜 Known Issues and FAQ's", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FFrom%20figma%20export%20create%20react%20site.one%7C20a7ebb2-7c15-43a3-b825-b08601dc3d13%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cdd624d19-3c4a-44f7-87e7-504cac96d362%2F%29", "section_group": "POCs"}}, {"text": "n a high-quality codebase.\nLastly, updates to the Figma design may require re-exporting and regenerating components, which can overwrite manual changes. To avoid losing work, document any customizations and consider using version control branches to manage updates. Communicate with your design team to coordinate changes and minimize disruption to the development process.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "📜 Known Issues and FAQ's", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FFrom%20figma%20export%20create%20react%20site.one%7C20a7ebb2-7c15-43a3-b825-b08601dc3d13%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cdd624d19-3c4a-44f7-87e7-504cac96d362%2F%29", "section_group": "POCs"}}, {"text": "🧩 Use Cases and Example Code\n\n\n\n\nThis workflow is ideal for rapidly prototyping marketing sites, landing pages, dashboards, and internal tools where design fidelity and speed are paramount. For example, a startup launching a new product can use this process to quickly turn a Figma mockup into a live site, iterating on feedback with minimal delay. Agencies can also benefit by delivering client projects faster, with fewer handoff issues between design and development.\nA typical use case involves exporting a Figma landing page as a PDF, uploading it to V0, and generating a set of React components. These components can then be imported into a Next.js page, such as pages/index.js, to render the homepage. The generated code often includes layout containers, styled buttons, and responsive grids that closely match the original design.\nHere’s a simplified example of how you might use a V0-generated component in Next.js:\njsx\nCopy Code\nimport HeroSection from '../components/HeroSection';  ￼  ￼exp", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FFrom%20figma%20export%20create%20react%20site.one%7C20a7ebb2-7c15-43a3-b825-b08601dc3d13%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C966352ea-bbc5-45ae-acf0-912948460dd5%2F%29", "section_group": "POCs"}}, {"text": "hat closely match the original design.\nHere’s a simplified example of how you might use a V0-generated component in Next.js:\njsx\nCopy Code\nimport HeroSection from '../components/HeroSection';  ￼  ￼export default function Home() {  ￼  return (  ￼    <main>  ￼      <HeroSection />  ￼      {/* Add more generated components here */}  ￼    </main>  ￼  );  ￼}  ￼\nYou can further enhance the generated code by integrating state management, API calls, or custom hooks as needed. For instance, if your Figma design includes a contact form, you can connect it to a backend service or third-party API for form submissions. The generated components serve as a solid foundation, allowing you to focus on business logic rather than layout.\nAnother use case is for design system documentation. By exporting Figma components and generating corresponding React code, you can create a living style guide that stays in sync with your design source. This is particularly useful for larger teams or organizations with m", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "🧩 Use Cases and Example Code", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FFrom%20figma%20export%20create%20react%20site.one%7C20a7ebb2-7c15-43a3-b825-b08601dc3d13%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C966352ea-bbc5-45ae-acf0-912948460dd5%2F%29", "section_group": "POCs"}}, {"text": "components and generating corresponding React code, you can create a living style guide that stays in sync with your design source. This is particularly useful for larger teams or organizations with multiple products sharing a common design language.\nFinally, this workflow supports iterative development. As your Figma design evolves, you can re-export updated PDFs and regenerate components, ensuring your codebase remains aligned with the latest design changes. This reduces technical debt and keeps your project visually consistent over time.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "🧩 Use Cases and Example Code", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FFrom%20figma%20export%20create%20react%20site.one%7C20a7ebb2-7c15-43a3-b825-b08601dc3d13%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C966352ea-bbc5-45ae-acf0-912948460dd5%2F%29", "section_group": "POCs"}}, {"text": "💡 Tips Tricks and Shortcuts\n\n\n\n\nTo maximize efficiency, organize your Figma files with clear naming conventions and consistent layer structures. This makes it easier for V0 to interpret your design and generate clean, maintainable code. Group related elements into components and use auto-layout features in Figma to ensure responsive behavior translates well into React.\nWhen exporting from Figma, select only the frames or pages you need, and use high-resolution settings to preserve detail. Avoid flattening layers unnecessarily, as this can make it harder for V0 to distinguish between interactive elements and static content. If possible, annotate your design with notes or comments to guide the code generation process.\nLeverage V0’s customization options to tailor the output to your project’s needs. For example, you can choose between different CSS-in-JS solutions, adjust naming conventions, or specify how assets are handled. Experiment with these settings to find the optimal configuratio", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FFrom%20figma%20export%20create%20react%20site.one%7C20a7ebb2-7c15-43a3-b825-b08601dc3d13%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C67001fe5-b22d-4c9c-8f80-40fa3fda44c6%2F%29", "section_group": "POCs"}}, {"text": "needs. For example, you can choose between different CSS-in-JS solutions, adjust naming conventions, or specify how assets are handled. Experiment with these settings to find the optimal configuration for your workflow.\nAfter generating code, use your code editor’s refactoring tools to quickly rename components, extract reusable logic, or apply consistent styling. Tools like VS Code’s multi-cursor editing and search-and-replace can save significant time when making global changes. Consider setting up code formatting and linting tools to enforce style consistency across your project.\nFor rapid iteration, set up hot reloading in your Next.js project so you can see changes in real time as you tweak the generated components. Use browser developer tools to inspect and debug layout issues, and leverage React DevTools to monitor component state and props.\nFinally, document your process and share shortcuts with your team. Create a checklist or template for new projects, outlining the steps fr", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FFrom%20figma%20export%20create%20react%20site.one%7C20a7ebb2-7c15-43a3-b825-b08601dc3d13%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C67001fe5-b22d-4c9c-8f80-40fa3fda44c6%2F%29", "section_group": "POCs"}}, {"text": "leverage React DevTools to monitor component state and props.\nFinally, document your process and share shortcuts with your team. Create a checklist or template for new projects, outlining the steps from Figma export to code integration. This ensures everyone follows best practices and can onboard quickly, reducing friction and improving collaboration.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FFrom%20figma%20export%20create%20react%20site.one%7C20a7ebb2-7c15-43a3-b825-b08601dc3d13%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C67001fe5-b22d-4c9c-8f80-40fa3fda44c6%2F%29", "section_group": "POCs"}}, {"text": "🔧 Setup and Configuration\n\n\n\n\nThis workflow is ideal for rapidly prototyping marketing sites, landing pages, dashboards, and internal tools where design fidelity and speed are paramount. For example, a startup launching a new product can use this process to quickly turn a Figma mockup into a live site, iterating on feedback with minimal delay. Agencies can also benefit by delivering client projects faster, with fewer handoff issues between design and development.\nA typical use case involves exporting a Figma landing page as a PDF, uploading it to V0, and generating a set of React components. These components can then be imported into a Next.js page, such as pages/index.js, to render the homepage. The generated code often includes layout containers, styled buttons, and responsive grids that closely match the original design.\nHere’s a simplified example of how you might use a V0-generated component in Next.js:\njsx\nCopy Code\nimport HeroSection from '../components/HeroSection';  ￼  ￼export", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FFrom%20figma%20export%20create%20react%20site.one%7C20a7ebb2-7c15-43a3-b825-b08601dc3d13%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cab3cd084-c587-4964-b4a1-5884bcea045a%2F%29", "section_group": "POCs"}}, {"text": "closely match the original design.\nHere’s a simplified example of how you might use a V0-generated component in Next.js:\njsx\nCopy Code\nimport HeroSection from '../components/HeroSection';  ￼  ￼export default function Home() {  ￼  return (  ￼    <main>  ￼      <HeroSection />  ￼      {/* Add more generated components here */}  ￼    </main>  ￼  );  ￼}  ￼\nYou can further enhance the generated code by integrating state management, API calls, or custom hooks as needed. For instance, if your Figma design includes a contact form, you can connect it to a backend service or third-party API for form submissions. The generated components serve as a solid foundation, allowing you to focus on business logic rather than layout.\nAnother use case is for design system documentation. By exporting Figma components and generating corresponding React code, you can create a living style guide that stays in sync with your design source. This is particularly useful for larger teams or organizations with mult", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "🔧 Setup and Configuration", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FFrom%20figma%20export%20create%20react%20site.one%7C20a7ebb2-7c15-43a3-b825-b08601dc3d13%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cab3cd084-c587-4964-b4a1-5884bcea045a%2F%29", "section_group": "POCs"}}, {"text": "ponents and generating corresponding React code, you can create a living style guide that stays in sync with your design source. This is particularly useful for larger teams or organizations with multiple products sharing a common design language.\nFinally, this workflow supports iterative development. As your Figma design evolves, you can re-export updated PDFs and regenerate components, ensuring your codebase remains aligned with the latest design changes. This reduces technical debt and keeps your project visually consistent over time.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "🔧 Setup and Configuration", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FFrom%20figma%20export%20create%20react%20site.one%7C20a7ebb2-7c15-43a3-b825-b08601dc3d13%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cab3cd084-c587-4964-b4a1-5884bcea045a%2F%29", "section_group": "POCs"}}, {"text": "🧩 Use Cases and Example Code\n\n\n\n\nThe github repository is here for the three examples: https://github.com/creospan-inc/ds-ollama-local", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG-PythonExamples", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FRAG-PythonExamples.one%7C48a94ebf-3135-4b36-afc0-59be260230fe%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C9df6679f-b07b-47d4-b9c3-ab5acf17f2b4%2F%29", "section_group": "POCs"}}, {"text": "📖 Overview\n\n\n\n\nI created three different python scripts that are examples of retrieval augmented generation (RAG).\n\nFirst example: This is a CSV file with information about pizza places. So that's an example of leveraging data that could be structured. It could be overviews of the different pizza places, including information on the location, menu, customer reviews, etc..\n \n\n\nSecond example: Taking a large text file with information about artificial intelligence embedding that with a vector embedding and then turning that into a collection in a SQLite database and then running queries on that. \n\nThird example:  This a directory full of files about how to become more calm under pressure.  vectorizing that and being able to ask questions on that data. \n\n\nAll of this is meant to show how you can take things like a company's standard operating procedures for onboarding or whatever and then without having to send it into the cloud if it's sensitive data be able to embed it and then run smal", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG-PythonExamples", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FRAG-PythonExamples.one%7C48a94ebf-3135-4b36-afc0-59be260230fe%2F%F0%9F%93%96%20Overview%7Ccc389a2f-1683-4f41-a130-64222ded646d%2F%29", "section_group": "POCs"}}, {"text": "can take things like a company's standard operating procedures for onboarding or whatever and then without having to send it into the cloud if it's sensitive data be able to embed it and then run smaller models for Q&A on it. Further on we will take this and move it to more powerful vector databases like Weeviate locally and things like that. We'll expand on it. But basically these are a few different examples of how that's done in a very simple way. Also very valuable for people that might want to learn these techniques and get a better sense of what all this stuff is about and how it works all together.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG-PythonExamples", "page": "📖 Overview", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FRAG-PythonExamples.one%7C48a94ebf-3135-4b36-afc0-59be260230fe%2F%F0%9F%93%96%20Overview%7Ccc389a2f-1683-4f41-a130-64222ded646d%2F%29", "section_group": "POCs"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG-PythonExamples", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FRAG-PythonExamples.one%7C48a94ebf-3135-4b36-afc0-59be260230fe%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cddef7463-3940-4843-94a9-d89aa5d1ed3b%2F%29", "section_group": "POCs"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG-PythonExamples", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FRAG-PythonExamples.one%7C48a94ebf-3135-4b36-afc0-59be260230fe%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Cd10dc58a-c199-4731-8995-8faf2f709f63%2F%29", "section_group": "POCs"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG-PythonExamples", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28POCs%2FRAG-PythonExamples.one%7C48a94ebf-3135-4b36-afc0-59be260230fe%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cfada17c2-e956-463c-a25f-6b7407de408b%2F%29", "section_group": "POCs"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "GitHub CoPilot", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FGitHub%20CoPilot.one%7Cb8ceb30d-e555-234f-b6c2-0d46e4585132%2F%F0%9F%93%96%20Overview%7C1a5324fd-9783-fd4c-88c4-265090138461%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "GitHub CoPilot", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FGitHub%20CoPilot.one%7Cb8ceb30d-e555-234f-b6c2-0d46e4585132%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cf79bf156-a60d-9d4e-a461-c6a40918f248%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "GitHub CoPilot", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FGitHub%20CoPilot.one%7Cb8ceb30d-e555-234f-b6c2-0d46e4585132%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C1aa49f2b-338c-ac44-bcab-7d1dc717fd86%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "GitHub CoPilot", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FGitHub%20CoPilot.one%7Cb8ceb30d-e555-234f-b6c2-0d46e4585132%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Cea258225-d552-2545-b748-ebd1f1657661%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "GitHub CoPilot", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FGitHub%20CoPilot.one%7Cb8ceb30d-e555-234f-b6c2-0d46e4585132%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C4b15ff69-62c1-1643-b5a6-8a7a6029d81c%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "GitHub Copilot for user stories\n\n\n\n\n\nWhile GitHub doesn't have a built-in chat feature like Slack or Microsoft Teams, you can still create and manage user stories directly within GitHub using issues and project boards. Here's how you can make the most of GitHub for story creation and collaboration:\nCreating User Stories in GitHub\n\nIssues: Create an issue for each user story. Include detailed descriptions, acceptance criteria, and any relevant attachments or links.\nProject Boards: Use GitHub project boards to organize and track your user stories. You can create columns for different stages (e.g., To Do, In Progress, Done) and move issues between columns as they progress.\nComments: Collaborate with your team by using the comments section within each issue. You can discuss details, ask questions, and provide updates.\n\nEnhancing Collaboration\n\nNotifications: GitHub will notify team members of updates to issues they are watching or assigned to, helping keep everyone in the loop.\nLabels and", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "GitHub CoPilot", "page": "GitHub Copilot for user stories", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FGitHub%20CoPilot.one%7Cb8ceb30d-e555-234f-b6c2-0d46e4585132%2FGitHub%20Copilot%20for%20user%20stories%7C2ee0554b-1fa9-0146-adf3-61fcc43e62f6%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "ons, and provide updates.\n\nEnhancing Collaboration\n\nNotifications: GitHub will notify team members of updates to issues they are watching or assigned to, helping keep everyone in the loop.\nLabels and Milestones: Use labels to categorize issues (e.g., bug, enhancement, user story) and milestones to group related issues and track progress towards larger goals.\nGitHub Discussions: For more extensive conversations, you can use GitHub Discussions, which allows for threaded discussions on various topics related to your project.\n\nWould you like guidance on setting up your first user story issue or creating a project board?", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "GitHub CoPilot", "page": "GitHub Copilot for user stories", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FGitHub%20CoPilot.one%7Cb8ceb30d-e555-234f-b6c2-0d46e4585132%2FGitHub%20Copilot%20for%20user%20stories%7C2ee0554b-1fa9-0146-adf3-61fcc43e62f6%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2F%F0%9F%93%96%20Overview%7Cc2ebc92a-3dde-6946-8dc5-34abc9a0489e%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cafe167a1-c253-2249-8179-261a09f088db%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca4f1d373-7979-6248-bcc1-aabbae9a7d63%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C563e800e-fdba-b943-a908-9390b6a8a600%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7C93495d17-0b66-6a41-a496-39fb59a05f2b%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "More overview\n\n\n\n\n\nIf you want an AI tool that helps you create user stories and put them directly into a GitHub project, there are several options available, ranging from general-purpose AI chatbots to specialized tools. Here are the most relevant solutions:\n\n## Tools That Generate User Stories and Integrate with GitHub\n\n| Tool                | User Story Generation | GitHub Integration | Notes |\n|---------------------|----------------------|--------------------|-------|\n| **StoryBot AI**     | Yes                  | Yes                | CLI tool; generates user stories using AI and creates GitHub issues directly via the GitHub CLI[6]. |\n| **ProdOps.AI**      | Yes                  | Yes                | Integrates with your GitHub repo, generates user stories and acceptance criteria specific to your codebase[2]. |\n\n### Details\n\n**StoryBot AI**\n- Command-line tool that uses OpenAI models to generate user stories in Markdown or BDD format.\n- Lets you create GitHub issues directly from", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "More overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2FMore%20overview%7Cd6f57bd9-9d03-6741-8f7c-10bf144538f3%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "ific to your codebase[2]. |\n\n### Details\n\n**StoryBot AI**\n- Command-line tool that uses OpenAI models to generate user stories in Markdown or BDD format.\n- Lets you create GitHub issues directly from your terminal using the `gh` CLI tool.\n- Highly customizable via prompts and context flags[6].\n\n**ProdOps.AI**\n- AI tool that connects to your GitHub repository.\n- Generates user stories and acceptance criteria tailored to your actual codebase and product.\n- Keeps your information confidential and is specifically designed for product management workflows[2].\n\n## Other AI Tools for User Story Generation\n\nIf you only need to generate user stories (not necessarily push them to GitHub automatically), consider:\n- **<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Gemini**: General AI chatbots that generate user stories from prompts[1][8].\n- **StoriesOnBoard**: AI-assisted story mapping and user story generation, but manual export to GitHub is required[3][9].\n- **Easy User Story Generator, ClickUp AI, ProductGo**: Tools", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "More overview", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2FMore%20overview%7Cd6f57bd9-9d03-6741-8f7c-10bf144538f3%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "prompts[1][8].\n- **StoriesOnBoard**: AI-assisted story mapping and user story generation, but manual export to GitHub is required[3][9].\n- **Easy User Story Generator, ClickUp AI, ProductGo**: Tools focused on generating user stories and acceptance criteria; some offer integrations with project management tools, but not always direct GitHub integration[1][4][5][7].\n\n## Recommendation\n\n- For seamless creation and direct GitHub integration, **StoryBot AI** and **ProdOps.AI** are the best fits.\n- If you want a visual workflow or advanced backlog management, use **StoriesOnBoard** or similar tools, then manually sync with GitHub.\n\nThese AI tools can significantly streamline your Agile process by automating user story creation and, in some cases, pushing them directly into your GitHub project for immediate team use[1][2][6].\n\nSources\n[1] Generate User Stories Using AI | 21 AI Prompts + 15 Tips - Agilemania https://agilemania.com/how-to-create-user-stories-using-ai\n[2] Gen AI for User Stori", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "More overview", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2FMore%20overview%7Cd6f57bd9-9d03-6741-8f7c-10bf144538f3%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "for immediate team use[1][2][6].\n\nSources\n[1] Generate User Stories Using AI | 21 AI Prompts + 15 Tips - Agilemania https://agilemania.com/how-to-create-user-stories-using-ai\n[2] Gen AI for User Stories : r/ProductManagement - Reddit https://www.reddit.com/r/ProductManagement/comments/1e7z2z4/gen_ai_for_user_stories/\n[3] Generate User Stories Like A Pro With AI Assistance - StoriesOnBoard https://storiesonboard.com/blog/generate-user-stories\n[4] Free AI Agile User Story Generator - Easy-Peasy.AI https://easy-peasy.ai/templates/user-story\n[5] AI User Story Generator | ClickUp Brain https://clickup.com/features/ai/user-story-generator\n[6] revelrylabs/storybot-ai: AI Generated User Stories from your CLI https://github.com/revelrylabs/storybot-ai\n[7] Create User Stories Using AI – What You Should Consider https://community.atlassian.com/forums/App-Central-articles/Create-User-Stories-Using-AI-What-You-Should-Consider/ba-p/2762462\n[8] AI Enhanced Agile Practices: Artificial Intelligence in", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "More overview", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2FMore%20overview%7Cd6f57bd9-9d03-6741-8f7c-10bf144538f3%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "uld Consider https://community.atlassian.com/forums/App-Central-articles/Create-User-Stories-Using-AI-What-You-Should-Consider/ba-p/2762462\n[8] AI Enhanced Agile Practices: Artificial Intelligence in Action https://rahighi.ir/blog/ai-enhanced-agile-practices-artificial-intelligence-in-action/\n[9] Free AI User Story Generator - StoriesOnBoard https://storiesonboard.com/ai-user-story-generator.html", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "More overview", "chunk_index": 4, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2FMore%20overview%7Cd6f57bd9-9d03-6741-8f7c-10bf144538f3%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "Leading PM tools as of 4/22/25\n\n\n\n\n\n## Leading AI Tools for Product Managers (XP Teams)\n\nProduct Managers—especially those working in cross-functional, agile (XP) teams—have a growing ecosystem of AI tools to streamline their workflows, enhance collaboration, and drive better product outcomes. Below is a summary of top AI tools, their core uses, and unique strengths relevant to XP-style teams.\n\n**General-Purpose AI Assistants**\n\n- **ChatGPT (OpenAI)**\n  - Versatile for brainstorming, idea prioritization, user research, data analysis, meeting prep, documentation, and content generation.\n  - Supports plugins for expanded capabilities.\n  - Widely used for automating repetitive tasks and enhancing team productivity[1][2][3][5][6].\n\n- **Google Gemini**\n  - Excels at research, information gathering, multilingual support, and integration with Google Workspace.\n  - Useful for competitive analysis, market research, and supporting global teams[1][6].\n\n**Product Management-Specific AI Tools**\n\n-", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2FLeading%20PM%20tools%20as%20of%204%5C%2F22%5C%2F25%7C74e5acd7-1651-2b4d-8946-a65fb31357d3%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "ing, multilingual support, and integration with Google Workspace.\n  - Useful for competitive analysis, market research, and supporting global teams[1][6].\n\n**Product Management-Specific AI Tools**\n\n- **ProdPad CoPilot**\n  - Built specifically for product management.\n  - Analyzes customer feedback, identifies recurring themes, prioritizes initiatives, and generates product documentation.\n  - Deeply integrates with roadmaps and backlogs, making it highly relevant for XP teams managing rapid iterations[3].\n\n- **ChatPRD**\n  - AI-powered creation and improvement of Product Requirements Documents (PRDs).\n  - Offers templates, structure suggestions, and coaching for newer product managers[1][5].\n\n- **Delibr**\n  - AI-assisted writing and planning for product specifications, user stories, and documentation.\n  - Enhances collaboration during feature refinement, supporting XP practices[5].\n\n- **Jam**\n  - AI-driven bug detection and reporting.\n  - Streamlines communication between product and engi", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2FLeading%20PM%20tools%20as%20of%204%5C%2F22%5C%2F25%7C74e5acd7-1651-2b4d-8946-a65fb31357d3%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "entation.\n  - Enhances collaboration during feature refinement, supporting XP practices[5].\n\n- **Jam**\n  - AI-driven bug detection and reporting.\n  - Streamlines communication between product and engineering teams, reducing friction in agile environments[1].\n\n**AI for Data Insights and Automation**\n\n- **H2O.ai**\n  - Focuses on predictive modeling, classification, clustering, and AutoML.\n  - Enables data-driven decision-making, customer behavior analysis, and integrates with analytics tools for seamless deployment[2].\n\n- **Monterey AI**\n  - Analyzes large datasets (calls, emails, chats) for actionable customer insights.\n  - Supports data-driven decisions and enhances user experience research[5].\n\n**AI for Prototyping and Engineering Collaboration**\n\n- **GitHub Copilot & Cursor**\n  - AI coding assistants that help generate code, automate prototyping, and support rapid iteration.\n  - Useful for PMs working closely with engineering in XP teams to quickly validate ideas and build prototypes", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2FLeading%20PM%20tools%20as%20of%204%5C%2F22%5C%2F25%7C74e5acd7-1651-2b4d-8946-a65fb31357d3%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "ng assistants that help generate code, automate prototyping, and support rapid iteration.\n  - Useful for PMs working closely with engineering in XP teams to quickly validate ideas and build prototypes[8].\n\n**AI-Powered Search and Research**\n\n- **KOMO Search**\n  - AI-powered search engine for market research, competitor analysis, and industry insights[5].\n\n## Comparison Table\n\n| Tool             | Main Use Cases                                  | Best For XP Teams Because...           |\n|------------------|------------------------------------------------|----------------------------------------|\n| ChatGPT          | Brainstorming, docs, research, automation       | Versatile, fast iteration, team-wide   |\n| ProdPad CoPilot  | Feedback analysis, prioritization, docs         | Deep roadmap/backlog integration       |\n| ChatPRD          | PRD creation, documentation                     | Standardizes requirements, saves time  |\n| Delibr           | User stories, specs, collaboration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2FLeading%20PM%20tools%20as%20of%204%5C%2F22%5C%2F25%7C74e5acd7-1651-2b4d-8946-a65fb31357d3%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "cklog integration       |\n| ChatPRD          | PRD creation, documentation                     | Standardizes requirements, saves time  |\n| Delibr           | User stories, specs, collaboration              | Streamlines feature refinement         |\n| Jam              | Bug reporting, tracking                         | Reduces PM–engineering friction        |\n| H2O.ai           | Predictive analytics, data-driven insights      | Enables rapid, informed decisions      |\n| Monterey AI      | Customer insights from unstructured data        | Informs user stories, priorities       |\n| GitHub Copilot   | Code generation, prototyping                    | Accelerates technical validation       |\n| Google Gemini    | Research, multilingual support                  | Cross-functional/global collaboration  |\n| KOMO Search      | Market/competitor research                      | Keeps team up-to-date on trends        |\n\n## Summary\n\nAI tools for Product Managers now span from general-purpose assis", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 4, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2FLeading%20PM%20tools%20as%20of%204%5C%2F22%5C%2F25%7C74e5acd7-1651-2b4d-8946-a65fb31357d3%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "ration  |\n| KOMO Search      | Market/competitor research                      | Keeps team up-to-date on trends        |\n\n## Summary\n\nAI tools for Product Managers now span from general-purpose assistants like ChatGPT and Google Gemini to specialized platforms like ProdPad CoPilot, ChatPRD, and Delibr. These tools support XP teams by automating routine tasks, enhancing collaboration, providing actionable insights, and streamlining documentation and communication with engineering. Selecting the right mix depends on your team's workflow, integration needs, and product complexity[1][2][3][5].\n\nSources\n[1] Best AI Tools for Product Managers in 2025 - Learn Prompting https://learnprompting.org/blog/ai-tools-for-product-managers\n[2] Top 21 AI Tools for Product Managers and Product Teams https://productschool.com/blog/artificial-intelligence/ai-tools-for-product-managers\n[3] 15 Best AI Tools For Product Managers - ProdPad https://www.prodpad.com/blog/ai-tools-for-product-managers/\n[4] How to", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 5, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2FLeading%20PM%20tools%20as%20of%204%5C%2F22%5C%2F25%7C74e5acd7-1651-2b4d-8946-a65fb31357d3%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "/productschool.com/blog/artificial-intelligence/ai-tools-for-product-managers\n[3] 15 Best AI Tools For Product Managers - ProdPad https://www.prodpad.com/blog/ai-tools-for-product-managers/\n[4] How to gain experience in AI as a PM? : r/ProductManagement https://www.reddit.com/r/ProductManagement/comments/1ic2and/how_to_gain_experience_in_ai_as_a_pm/\n[5] AI for Product Managers: 5 Foundational Concepts & 10 Tool Options https://www.icagile.com/resources/ai-for-product-managers-5-foundational-concepts-and-10-tool-options\n[6] How to Become an AI Product Manager? - Agilemania https://agilemania.com/how-to-become-ai-product-manager\n[7] Top AI Tools for PMs & The Future of Product Management - YouTube https://www.youtube.com/watch?v=Xpq4LEx8ymA\n[8] A guide to AI prototyping for product managers - <PERSON>'s Newsletter https://www.lennysnewsletter.com/p/a-guide-to-ai-prototyping-for-product", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 6, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2FLeading%20PM%20tools%20as%20of%204%5C%2F22%5C%2F25%7C74e5acd7-1651-2b4d-8946-a65fb31357d3%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "- <PERSON>'s Newsletter https://www.lennysnewsletter.com/p/a-guide-to-ai-prototyping-for-product", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 7, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Product%20Manager%20Tools%2FOverview.one%7Cc388e4e5-14fe-5b43-9e81-3209f8b7ddad%2FLeading%20PM%20tools%20as%20of%204%5C%2F22%5C%2F25%7C74e5acd7-1651-2b4d-8946-a65fb31357d3%2F%29", "section_group": "Product Manager <PERSON>ls"}}, {"text": "📖 Overview", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Template <PERSON>s", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Templates%2FTemplate%20Pages.one%7Ced8aee39-4bb9-764f-820d-49892befb07e%2F%F0%9F%93%96%20Overview%7C1027fc94-0514-454c-bf7f-9c2af9a6de55%2F%29", "section_group": "Templates"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Template <PERSON>s", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Templates%2FTemplate%20Pages.one%7Ced8aee39-4bb9-764f-820d-49892befb07e%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C195d11dd-28cf-504d-9026-3946cfcd0435%2F%29", "section_group": "Templates"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Template <PERSON>s", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Templates%2FTemplate%20Pages.one%7Ced8aee39-4bb9-764f-820d-49892befb07e%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cb925f648-d0aa-684a-8048-a8eb4c58c6bf%2F%29", "section_group": "Templates"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Template <PERSON>s", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Templates%2FTemplate%20Pages.one%7Ced8aee39-4bb9-764f-820d-49892befb07e%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C6ba5b557-2bd5-e941-9933-68471cfe2cfe%2F%29", "section_group": "Templates"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Template <PERSON>s", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Templates%2FTemplate%20Pages.one%7Ced8aee39-4bb9-764f-820d-49892befb07e%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cc2b10642-c1c2-394b-a946-7ac215891596%2F%29", "section_group": "Templates"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FUX%20Pilot.one%7Ce349d1ef-24aa-eb40-b7dd-0e9e591fb8f7%2F%F0%9F%93%96%20Overview%7C675d169d-27b6-5f47-af96-00d5f64cf6bc%2F%29", "section_group": "UX tools"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FUX%20Pilot.one%7Ce349d1ef-24aa-eb40-b7dd-0e9e591fb8f7%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C626ee1e5-e76a-624f-8f5c-12b9fa588df0%2F%29", "section_group": "UX tools"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FUX%20Pilot.one%7Ce349d1ef-24aa-eb40-b7dd-0e9e591fb8f7%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cdf8755c4-5271-8145-8227-85cb9514220c%2F%29", "section_group": "UX tools"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FUX%20Pilot.one%7Ce349d1ef-24aa-eb40-b7dd-0e9e591fb8f7%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7Cfb2b2056-710f-1a46-9c78-380c48930ab9%2F%29", "section_group": "UX tools"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FUX%20Pilot.one%7Ce349d1ef-24aa-eb40-b7dd-0e9e591fb8f7%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cbeddfe5a-22a4-d14a-82a1-b6e51bdf0edc%2F%29", "section_group": "UX tools"}}, {"text": "UX Pilot vs V.0\n\n\n\n\n\n## UX Pilot vs. V0.dev for UI Design\n\n### **Overview**\n\nBoth UX Pilot and V0.dev are AI-powered tools designed to accelerate UI creation, but they target slightly different audiences and use cases.\n\n### **Feature Comparison**\n\n| Feature/Aspect                  | UX Pilot                                    | V0.dev                                |\n|---------------------------------|---------------------------------------------|---------------------------------------|\n| Target Audience                 | Designers & UX professionals                | Developers                            |\n| UI Generation                   | Advanced, supports wireframes, flows, rich UIs | Basic, fast, functional UIs           |\n| Customization                   | Extensive (themes, flows, wireframes)       | Limited (mainly component-level)      |\n| Code Output                     | HTML, Figma plugin integration              | Production-ready JavaScript           |\n| Design Analysis", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "UX Pilot vs V.0", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FUX%20Pilot.one%7Ce349d1ef-24aa-eb40-b7dd-0e9e591fb8f7%2FUX%20Pilot%20vs%20V.0%7C30b77cf7-12c9-c84e-9553-2d97081e9b1b%2F%29", "section_group": "UX tools"}}, {"text": "wireframes)       | Limited (mainly component-level)      |\n| Code Output                     | HTML, Figma plugin integration              | Production-ready JavaScript           |\n| Design Analysis Tools           | Yes (Design Review, Predictive Heatmap)     | No                                    |\n| Workflow Integration            | Figma export, UX tools                      | Quick code export for dev workflows   |\n| Visual Design                   | More refined, supports complex screens      | More basic, limited customization     |\n| Pricing                         | Freemium, premium from $12/month            | Freemium, premium from $20/month      |\n\n### **Strengths of Each Tool**\n\n**UX Pilot:**\n- Offers advanced UX features like wireframing, user flows, and design review[1][3].\n- Integrates with Figma for seamless design handoff[1][3].\n- Provides tools for predictive heatmaps and accessibility checks[1].\n- Better for complex UI needs and for teams focused on user experien", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "UX Pilot vs V.0", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FUX%20Pilot.one%7Ce349d1ef-24aa-eb40-b7dd-0e9e591fb8f7%2FUX%20Pilot%20vs%20V.0%7C30b77cf7-12c9-c84e-9553-2d97081e9b1b%2F%29", "section_group": "UX tools"}}, {"text": "].\n- Integrates with Figma for seamless design handoff[1][3].\n- Provides tools for predictive heatmaps and accessibility checks[1].\n- Better for complex UI needs and for teams focused on user experience[1][3].\n- Allows more granular editing and iteration of designs[1][4].\n\n**V0.dev:**\n- Prioritizes speed and code generation for developers[1][3].\n- Generates production-ready JavaScript components quickly[1][3].\n- Best for simple UI generation and rapid prototyping[1][3].\n- Limited to the ShadCN UI system and less visual customization[3].\n\n### **Limitations**\n\n- UX Pilot’s interface can feel clunky and may have a steeper learning curve, though it is actively being improved[5].\n- V0.dev is not suitable for creating complex user flows or highly customized visual designs[1][3].\n\n### **Which Is Better?**\n\n- **UX Pilot is generally considered better for UI design** if your focus is on comprehensive user experience, complex flows, advanced customization, and design collaboration. It is particu", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "UX Pilot vs V.0", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FUX%20Pilot.one%7Ce349d1ef-24aa-eb40-b7dd-0e9e591fb8f7%2FUX%20Pilot%20vs%20V.0%7C30b77cf7-12c9-c84e-9553-2d97081e9b1b%2F%29", "section_group": "UX tools"}}, {"text": "Better?**\n\n- **UX Pilot is generally considered better for UI design** if your focus is on comprehensive user experience, complex flows, advanced customization, and design collaboration. It is particularly suited for designers and teams who need more than just quick component generation[1][3][6].\n\n- **V0.dev is better** if you are a developer needing to quickly scaffold simple UIs and want ready-to-use JavaScript code, with less emphasis on detailed design or advanced UX features[1][3].\n\n> “UX Pilot excels in providing a user-friendly interface, advanced UX design features, and integration with design tools. It is particularly well-suited for designers who require complex UI designs, wireframes, and in-depth design analysis. V0.dev is a strong contender for developers seeking a fast and efficient tool for generating simple UIs with Javascript output.”[1]\n\n### **Conclusion**\n\nUX Pilot is the superior choice for most UI design needs, especially where UX, complexity, and design handoff ma", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "UX Pilot vs V.0", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FUX%20Pilot.one%7Ce349d1ef-24aa-eb40-b7dd-0e9e591fb8f7%2FUX%20Pilot%20vs%20V.0%7C30b77cf7-12c9-c84e-9553-2d97081e9b1b%2F%29", "section_group": "UX tools"}}, {"text": "ficient tool for generating simple UIs with Javascript output.”[1]\n\n### **Conclusion**\n\nUX Pilot is the superior choice for most UI design needs, especially where UX, complexity, and design handoff matter. V0.dev is preferable for developer-centric, code-first, and rapid prototyping scenarios[1][3]. Try both to see which fits your workflow best.\n\nSources\n[1] Comparing UX Pilot and V0.dev - Adam Fard UX Studio https://adamfard.com/blog/ux-pilot-vs-v0-dev\n[2] Generative UI Design Tools: A Day's Review https://www.unnawut.com/posts/2025-01-05-generative-ui-review/\n[3] Top AI UI Generator: For a faster UI Design in 2025 https://adamfard.com/blog/ai-ui-generator\n[4] I Tested 4 Free Generative AI Tools for No-Code UI https://aitoolsclub.com/i-tested-4-free-generative-ai-tools-for-no-code-ui-heres-what-i-found/\n[5] I tested the 3 most popular AI tools for UX so you don't have to https://www.linkedin.com/pulse/i-tested-3-most-popular-ai-tools-uxso-you-dont-have-mi<PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>\n[6] Top AI", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "UX Pilot vs V.0", "chunk_index": 4, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FUX%20Pilot.one%7Ce349d1ef-24aa-eb40-b7dd-0e9e591fb8f7%2FUX%20Pilot%20vs%20V.0%7C30b77cf7-12c9-c84e-9553-2d97081e9b1b%2F%29", "section_group": "UX tools"}}, {"text": "s-what-i-found/\n[5] I tested the 3 most popular AI tools for UX so you don't have to https://www.linkedin.com/pulse/i-tested-3-most-popular-ai-tools-uxso-you-dont-have-mi<PERSON><PERSON>-<PERSON><PERSON><PERSON>-y<PERSON><PERSON>\n[6] Top AI UX/UI Design Tools for v0 in 2025 - Slashdot https://slashdot.org/software/ai-ux-ui-design-tools/for-v0/\n[7] Which AI tools have provided you with real added value? : r/UXDesign https://www.reddit.com/r/UXDesign/comments/1evwuoj/after_the_hype_which_ai_tools_have_provided_you/\n[8] UX Pilot - Superfast UX/UI Design with AI https://uxpilot.ai", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "UX Pilot vs V.0", "chunk_index": 5, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FUX%20Pilot.one%7Ce349d1ef-24aa-eb40-b7dd-0e9e591fb8f7%2FUX%20Pilot%20vs%20V.0%7C30b77cf7-12c9-c84e-9553-2d97081e9b1b%2F%29", "section_group": "UX tools"}}, {"text": "UX Pilot\n\n\n\n\n\n- Offers advanced UX features like wireframing, user flows, and design review[1][3].\n- Integrates with Figma for seamless design handoff[1][3].\n- Provides tools for predictive heatmaps and accessibility checks[1].\n- Better for complex UI needs and for teams focused on user experience[1][3].\n- Allows more granular editing and iteration of designs[1][4].\n\n- UX Pilot’s interface can feel clunky and may have a steeper learning curve, though it is actively being improved[5].", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "UX Pilot", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FUX%20Pilot.one%7Ce349d1ef-24aa-eb40-b7dd-0e9e591fb8f7%2FUX%20Pilot%7Ca63530bf-2275-8e4d-a14d-e4b0653aa0e7%2F%29", "section_group": "UX tools"}}, {"text": "📜 Known Issues and FAQ's\n\n\n\n\n\n1. **Issue**: Generated code may not always follow project-specific design systems.\n   - **Workaround**: Customize the output manually or update prompt with specific framework/language requirements.\n\n2. **Issue**: Components using third-party libraries (e.g., React Icons) might not include import statements.\n   - **Solution**: Manually add necessary imports based on component usage.\n\n3. **FAQ**: Can I use V.0 with Vue or Svelte?\n   - **Answer**: Yes, although React is the default, support for other frameworks is in progress or available with limitations.\n\n4. **Issue**: Some prompts return overly simplified layouts.\n   - **Tip**: Add details like \"include responsive design, use Tailwind\" to generate more complex outputs.\n\n5. **FAQ**: Is there version control for generated outputs?\n   - **Answer**: V.0 itself doesn’t manage versions, but you can export to GitHub for full version control.\n\n6. **FAQ**: Can I use my own component library?\n   - **Answer**: Not d", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FV.0%20%28in%20IDE%20as%20well%5C%29.one%7C56dd0723-a755-9743-b90c-2b973f1738e8%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cda891deb-fcea-0f4c-988d-0439d052cdcf%2F%29", "section_group": "UX tools"}}, {"text": "r generated outputs?\n   - **Answer**: V.0 itself doesn’t manage versions, but you can export to GitHub for full version control.\n\n6. **FAQ**: Can I use my own component library?\n   - **Answer**: Not directly, but you can copy generated code into your project and refactor to match your library.\n\n7. **Issue**: V.0 occasionally stalls or fails to generate content.\n   - **Fix**: Refresh the browser or log out and back in to reset the session.\n\n8. **FAQ**: Does V.0 support dark mode generation?\n   - **Answer**: Yes, you can specify \"dark mode\" in your prompt to generate dark-themed components.\n\n9. **FAQ**: What’s the best way to handle layouts like grids or complex forms?\n   - **Answer**: Use prompts like “create a 3-column responsive grid layout using Tailwind” for more control.\n\n10. **FAQ**: Is there an API for V.0?\n   - **Answer**: As of now, V.0 is primarily UI-driven. An API may be introduced in future iterations depending on demand.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "📜 Known Issues and FAQ's", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FV.0%20%28in%20IDE%20as%20well%5C%29.one%7C56dd0723-a755-9743-b90c-2b973f1738e8%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cda891deb-fcea-0f4c-988d-0439d052cdcf%2F%29", "section_group": "UX tools"}}, {"text": "Is there an API for V.0?\n   - **Answer**: As of now, V.0 is primarily UI-driven. An API may be introduced in future iterations depending on demand.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "📜 Known Issues and FAQ's", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FV.0%20%28in%20IDE%20as%20well%5C%29.one%7C56dd0723-a755-9743-b90c-2b973f1738e8%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cda891deb-fcea-0f4c-988d-0439d052cdcf%2F%29", "section_group": "UX tools"}}, {"text": "💡 Tips Tricks and Shortcuts\n\n\n\n\n\n\nBe Specific with Prompts: Clearly describe the desired UI component, including elements, styles, and behaviors. Specific prompts yield more accurate results.\nUse Iterative Refinement: Start with a basic prompt and iteratively refine the generated code by providing additional instructions or modifications.\nLeverage Templates: Utilize V.0’s built-in templates for common components to accelerate development and maintain consistency.\nCombine Text and Visuals**: V.0 supports multimodal inputs. Include sketches or screenshots along with your prompt to guide the UI generation more precisely.\n\n\n5. **Use Component Libraries**: Leverage shadcn/ui and Tailwind CSS to create aesthetically pleasing and accessible components out-of-the-box.\n\n\n6. **Keyboard Navigation**: Familiarize yourself with V.0's keyboard shortcuts for faster interaction. For example:\n   - `Cmd + Enter` (Mac) / `Ctrl + Enter` (Windows) to submit prompts.\n   - `Esc` to close modals or cancel gen", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FV.0%20%28in%20IDE%20as%20well%5C%29.one%7C56dd0723-a755-9743-b90c-2b973f1738e8%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C72d0ff26-6c95-874b-b366-44dc0d8080f4%2F%29", "section_group": "UX tools"}}, {"text": "Familiarize yourself with V.0's keyboard shortcuts for faster interaction. For example:\n   - `Cmd + Enter` (Mac) / `Ctrl + Enter` (Windows) to submit prompts.\n   - `Esc` to close modals or cancel generation.\n\n\n7. **Sync with Git**: Use GitHub integration to automatically sync generated code with your repository, ensuring version control and collaboration.\n\n\n8. **Preview Responsiveness**: Use the built-in preview mode to view how components render on different devices and screen sizes.\n\n\n9. **Adjust Token Limit**: If output seems truncated, increase the token limit (if configurable) to allow more content in each generation.\n\n\n10. **Use Prompt History**: Reuse and tweak previous prompts to save time and maintain consistency across similar components.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FV.0%20%28in%20IDE%20as%20well%5C%29.one%7C56dd0723-a755-9743-b90c-2b973f1738e8%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C72d0ff26-6c95-874b-b366-44dc0d8080f4%2F%29", "section_group": "UX tools"}}, {"text": "🔧 Setup and Configuration\n\n\n\n\n\n\n\n\n1. **Accessing V.0**: Visit the [V.0 website](https://v0.dev/) and sign in using your Vercel account. If you don't have an account, you can create one for free.\n\n2. **Initiating a Project**: Once logged in, you can start a new project by entering a natural language prompt describing the UI component or page you wish to create. For example, \"Create a responsive login form with email and password fields.\"\n\n3. **Reviewing Generated Code**: V.0 will generate the corresponding code based on your prompt. You can review, edit, and refine the code within the interface.\n\n4. **Exporting or Deploying**: After finalizing the code, you have the option to export it for integration into your existing project or deploy it directly using Vercel's deployment capabilities.\n\n5. **Integrating with Existing Projects**: V.0 supports integration with frameworks like Next.js. You can incorporate the generated components into your projects by copying the code or using provided", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FV.0%20%28in%20IDE%20as%20well%5C%29.one%7C56dd0723-a755-9743-b90c-2b973f1738e8%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C9a7129d6-0702-ab4b-b34a-09dedca87d41%2F%29", "section_group": "UX tools"}}, {"text": "5. **Integrating with Existing Projects**: V.0 supports integration with frameworks like Next.js. You can incorporate the generated components into your projects by copying the code or using provided installation commands.\n\n6. **Managing Projects**: V.0 offers project management features, allowing you to organize, rename, and delete projects as needed.\n\n7. **Collaborating with Team Members**: For team projects, V.0 provides collaboration tools, enabling multiple users to work on the same project simultaneously.\n\n8. **Configuring Settings**: Customize your experience by adjusting settings such as preferred frameworks, styling options, and component libraries.\n\n9. **Utilizing Templates**: V.0 offers a range of templates for common UI components and pages, which can serve as starting points for your projects.\n\n10. **Accessing Documentation and Support**: For detailed guidance and troubleshooting, refer to the [V.0 documentation](https://v0.dev/docs) and community forums.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "🔧 Setup and Configuration", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FV.0%20%28in%20IDE%20as%20well%5C%29.one%7C56dd0723-a755-9743-b90c-2b973f1738e8%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C9a7129d6-0702-ab4b-b34a-09dedca87d41%2F%29", "section_group": "UX tools"}}, {"text": "or your projects.\n\n10. **Accessing Documentation and Support**: For detailed guidance and troubleshooting, refer to the [V.0 documentation](https://v0.dev/docs) and community forums.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "🔧 Setup and Configuration", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FV.0%20%28in%20IDE%20as%20well%5C%29.one%7C56dd0723-a755-9743-b90c-2b973f1738e8%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C9a7129d6-0702-ab4b-b34a-09dedca87d41%2F%29", "section_group": "UX tools"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n🔧🔧\n\n\n- Prioritizes speed and code generation for developers[1][3].\n- Generates production-ready JavaScript components quickly[1][3].\n- Best for simple UI generation and rapid prototyping[1][3].\n- Limited to the ShadCN UI system and less visual customization[3].\n\n\n- V0.dev is not suitable for creating complex user flows or highly customized visual designs[1][3].\n\n\n\nV.0 by Vercel is an AI-powered generative UI tool designed to streamline the frontend development process. It enables developers to create production-ready UI components and pages using natural language prompts. By leveraging technologies like React, Tailwind CSS, and shadcn/ui components, V.0 translates textual descriptions into functional code, facilitating rapid prototyping and development.\n\nOne of the standout features of V.0 is its chat-based interface, which allows users to interact with the tool conversationally. This interface supports the generation of various UI elements, from simple buttons to co", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FV.0%20%28in%20IDE%20as%20well%5C%29.one%7C56dd0723-a755-9743-b90c-2b973f1738e8%2F%F0%9F%93%96%20Overview%7Cf64536b8-b41c-9f45-a063-66559776509a%2F%29", "section_group": "UX tools"}}, {"text": "out features of V.0 is its chat-based interface, which allows users to interact with the tool conversationally. This interface supports the generation of various UI elements, from simple buttons to complex dashboards, based on user input. The tool is particularly beneficial for teams aiming to accelerate their development workflows and reduce the time from concept to deployment.\n\nV.0 supports integration with multiple frameworks, including Next.js, Vue, and Svelte. This flexibility ensures that developers can incorporate the generated components into their existing projects seamlessly. Additionally, V.0 offers the capability to handle multimodal inputs, such as text and images, enhancing its utility in diverse development scenarios.\n\nThe tool is tightly integrated with Vercel's ecosystem, allowing for direct deployment of generated projects. This integration simplifies the process of moving from development to production, making it an efficient solution for teams looking to streamline", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "📖 Overview", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FV.0%20%28in%20IDE%20as%20well%5C%29.one%7C56dd0723-a755-9743-b90c-2b973f1738e8%2F%F0%9F%93%96%20Overview%7Cf64536b8-b41c-9f45-a063-66559776509a%2F%29", "section_group": "UX tools"}}, {"text": "allowing for direct deployment of generated projects. This integration simplifies the process of moving from development to production, making it an efficient solution for teams looking to streamline their deployment pipelines.\n\nV.0 operates on a freemium model, offering a generous free tier suitable for experimentation and small projects. Paid plans provide additional features and increased usage limits, catering to the needs of larger teams and more complex projects.\n\nIn summary, V.0 represents a significant advancement in AI-assisted frontend development, offering a user-friendly interface, robust integration capabilities, and support for modern web technologies. Its ability to translate natural language prompts into functional code makes it a valuable tool for developers aiming to enhance their productivity and streamline their workflows.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "📖 Overview", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FV.0%20%28in%20IDE%20as%20well%5C%29.one%7C56dd0723-a755-9743-b90c-2b973f1738e8%2F%F0%9F%93%96%20Overview%7Cf64536b8-b41c-9f45-a063-66559776509a%2F%29", "section_group": "UX tools"}}, {"text": "nce their productivity and streamline their workflows.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "📖 Overview", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FV.0%20%28in%20IDE%20as%20well%5C%29.one%7C56dd0723-a755-9743-b90c-2b973f1738e8%2F%F0%9F%93%96%20Overview%7Cf64536b8-b41c-9f45-a063-66559776509a%2F%29", "section_group": "UX tools"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28UX%20tools%2FV.0%20%28in%20IDE%20as%20well%5C%29.one%7C56dd0723-a755-9743-b90c-2b973f1738e8%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca11ff9c1-81a5-1343-9935-a3b983a550fc%2F%29", "section_group": "UX tools"}}, {"text": "📜 Known Issues and FAQ's\n\n\n\n\n\nFrequently Asked Questions\n\nFrequently Asked Questions\nWhat distance metrics does Chroma use?\r\nChroma employs distance metrics where 0 indicates identical embeddings, and larger values indicate greater dissimilarity. Cosine similarity ranges from -1 to 1 (1 being maximum similarity), while dot product ranges from negative to positive infinity, typically normalized to 0–1 for non-negative vectors (Chroma Cookbook FAQ).\t\t\t\t\nHow does Chroma index embedding vectors?\r\nChroma uses a fork of the HNSW library for efficient indexing and a Brute Force index as a buffer.\t\t\t\t\nCan I change the collection dimensionality after it's set?\r\nNo, the dimensionality is fixed by the first embedding added to a collection (e.g., [1, 2, 3] sets it to 3) and cannot be altered afterward.\t\t\t\t\nCan I use transformers models with Chroma?\r\nYes, via SentenceTransformerEmbeddingFunction (e.g., FacebookAI/xlm-roberta-large). However, not all models are compatible, and normalization (normali", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cbeafe855-820c-1d47-9e51-474e07b7b41b%2F%29", "section_group": "Vector DBs"}}, {"text": ".\t\t\t\t\nCan I use transformers models with Chroma?\r\nYes, via SentenceTransformerEmbeddingFunction (e.g., FacebookAI/xlm-roberta-large). However, not all models are compatible, and normalization (normalize_embeddings=True) may be needed for optimal results.\t\t\t\t\nShould I store documents in Chroma?\nPros: Centralizes data, enables keyword searches.\nCons: Increases database size due to document duplication, may slow queries.\r\nBest for single-node/local setups in Chroma 0.5.x.\t\t\t\t\nWhere is my data stored in Chroma?\r\nStorage depends on the environment:\t\t\t\t\nCLI mode (no --path): ./chroma_data\nJupyter/Colab/PersistentClient (no path/env): ./chroma\nDocker compose: chroma-data volume\nDocker run (no -v): Data is lost when the container is removed\nOtherwise, use a specified directory via environment variables or parameters.\n\nKnown Issues and Solutions\n\n\nIssue\nDescription\nSolution\n\n\nCollection Dimensionality Mismatch\nError: chromadb.errors.InvalidDimensionException: Embedding dimension XXX does not ma", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📜 Known Issues and FAQ's", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cbeafe855-820c-1d47-9e51-474e07b7b41b%2F%29", "section_group": "Vector DBs"}}, {"text": "ables or parameters.\n\nKnown Issues and Solutions\n\n\nIssue\nDescription\nSolution\n\n\nCollection Dimensionality Mismatch\nError: chromadb.errors.InvalidDimensionException: Embedding dimension XXX does not match collection dimensionality YYY. Caused by inconsistent embedding dimensions.\nUse the same embedding function for all operations (e.g., default all-MiniLM-L6-v2).\n\n\nLarge Distances in Search Results\nSearch results show distances in the 10s or 100s due to non-normalized embeddings.\nNormalize embeddings using L2 norm (e.g., normalize_L2(vector) with numpy). Chroma uses L2 by default.\n\n\nOperationalError: collections.topic\nError: OperationalError: no such column: collections.topic. Caused by schema changes in Chroma 0.5.0.\nUpgrade all clients to version 0.5.x (Migration Guide).\n\n\nsqlite3.OperationalError: disk full\nError: sqlite3.OperationalError: database or disk is full. Caused by full disk or exhausted temp space (/tmp).\nIncrease disk space, clean up files, or set SQLITE_TMPDIR to a diffe", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📜 Known Issues and FAQ's", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cbeafe855-820c-1d47-9e51-474e07b7b41b%2F%29", "section_group": "Vector DBs"}}, {"text": "onalError: disk full\nError: sqlite3.OperationalError: database or disk is full. Caused by full disk or exhausted temp space (/tmp).\nIncrease disk space, clean up files, or set SQLITE_TMPDIR to a different location (SQLite Temp Files).\n\n\nRuntimeError: HTTP-only mode\nError: RuntimeError: Chroma is running in http-only client mode. Caused by using chromadb-client locally or dependency conflicts.\nUse chromadb for local setups, uninstall chromadb-client (pip uninstall chromadb-client), and use virtual environments.\n\n\nValueError: No embedding function\nError: ValueError: You must provide an embedding function to compute embeddings.\nSpecify an embedding function when creating/querying collections (e.g., DefaultEmbeddingFunction).\n\n\n\nBest Practices for Internal Team\nConsistency: Always use the same embedding function to avoid dimensionality issues.\nNormalization: Normalize embeddings to ensure accurate search results.\nDisk Management: Monitor disk space for local setups to prevent disk full err", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📜 Known Issues and FAQ's", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cbeafe855-820c-1d47-9e51-474e07b7b41b%2F%29", "section_group": "Vector DBs"}}, {"text": "embedding function to avoid dimensionality issues.\nNormalization: Normalize embeddings to ensure accurate search results.\nDisk Management: Monitor disk space for local setups to prevent disk full errors.\nVersion Control: Use compatible Chroma versions (e.g., 0.5.x) to avoid schema issues.\nVirtual Environments: Isolate dependencies to prevent conflicts, especially for HTTP-only mode errors.\n\n\nKnown Issues and Solutions\n\nBest Practices for Internal Team", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📜 Known Issues and FAQ's", "chunk_index": 4, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cbeafe855-820c-1d47-9e51-474e07b7b41b%2F%29", "section_group": "Vector DBs"}}, {"text": "💡 Tips Tricks and Shortcuts\n\n\n\n\n\nTips, Tricks, and Shortcuts for Chroma DB\nPerformance Optimization\n1. Batch Processing\nTip: Add documents in batches rather than one at a time for significant performance gains.\n# Inefficient: Adding one document at a time\nfor i, doc in enumerate(documents):\n embedding = model.encode(doc)\n collection.add(\n ids=[f\"doc_{i}\"],\n documents=[doc],\n embeddings=[embedding.tolist()]\n    )\n\n# Efficient: Adding documents in batches\nembeddings = model.encode(documents)\ncollection.add(\n ids=[f\"doc_{i}\" for i in range(len(documents))],\n documents=documents,\n embeddings=[embedding.tolist() for embedding in embeddings]\n)\n\n\n2. Optimizing Collection Size\nTip: For large collections, consider sharding your data into multiple collections based on categories or time periods.\n# Create multiple collections for different domains\nclient.delete_collection(\"technology_docs\")\ntech_collection = client.create_collection(\"technology_docs\")\nclient.delete_collection(\"health_docs\")\nhealt", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "reate multiple collections for different domains\nclient.delete_collection(\"technology_docs\")\ntech_collection = client.create_collection(\"technology_docs\")\nclient.delete_collection(\"health_docs\")\nhealth_collection = client.create_collection(\"health_docs\") \nclient.delete_collection(\"finance_docs\")\nfinance_collection = client.create_collection(\"finance_docs\")\n\nimport uuid\ndef generate_id(prefix=\"doc\"):\n return f\"{prefix}_{uuid.uuid4().hex[:8]}\"\n\n\n\ncategories = [\"technology\",\"health\"]\n# Route documents to appropriate collections\nfor doc, category in zip(documents, categories):\n if category == \"technology\":\n collection = tech_collection\n elif category == \"health\":\n collection = health_collection\n else:\n collection = finance_collection\n \n # Add document to the appropriate collection\n embedding = model.encode(doc).tolist()\n collection.add(\n ids=[generate_id()],\n documents=[doc],\n embeddings=[embedding]\n    )\n\n\n3. Memory Management\nTip: For RAM-constrained environments, use disk-based storage", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "model.encode(doc).tolist()\n collection.add(\n ids=[generate_id()],\n documents=[doc],\n embeddings=[embedding]\n    )\n\n\n3. Memory Management\nTip: For RAM-constrained environments, use disk-based storage and configure chunk size.\n\nAdvanced Query Techniques\n1. Hybrid Search with Keywords and Vectors\nTrick: Combine traditional keyword search with vector search for better results.\nimport re\nfrom sklearn.feature_extraction.text import TfidfVectorizer\nimport numpy as np\n\n# Function to perform hybrid search\ndef hybrid_search(query, collection, documents, alpha=0.5):\n # Get vector search results\n query_embedding = model.encode([query]).tolist()\n vector_results = collection.query(\n query_embeddings=query_embedding,\n n_results=10\n    )\n \n # Simple keyword matching (in a real scenario, use a proper keyword search)\n # Create TF-IDF vectorizer\n vectorizer = TfidfVectorizer(lowercase=True, stop_words='english')\n document_vectors = vectorizer.fit_transform(documents)\n query_vector = vectorizer.transform", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "ord search)\n # Create TF-IDF vectorizer\n vectorizer = TfidfVectorizer(lowercase=True, stop_words='english')\n document_vectors = vectorizer.fit_transform(documents)\n query_vector = vectorizer.transform([query])\n \n # Calculate similarities\n keyword_similarities = (document_vectors @ query_vector.T).toarray().flatten()\n \n # Get top indices from keyword search\n keyword_indices = np.argsort(keyword_similarities)[::-1][:10]\n \n # Combine results (this is a simplified approach)\n # Get document indices from vector search\n vector_indices = [int(id.split('_')[1]) for id in vector_results[\"ids\"][0]]\n \n # Create a scoring dictionary\n scores = {}\n for idx in vector_indices:\n scores[idx] = alpha\n \n for i, idx in enumerate(keyword_indices):\n if idx in scores:\n scores[idx] += (1 - alpha)\n else:\n scores[idx] = (1 - alpha)\n \n # Sort by combined score\n sorted_indices = sorted(scores.keys(), key=lambda x: scores[x], reverse=True)[:5]\n \n return [documents[idx] for idx in sorted_indices]\n\n\n2. Using Metadata", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "] = (1 - alpha)\n \n # Sort by combined score\n sorted_indices = sorted(scores.keys(), key=lambda x: scores[x], reverse=True)[:5]\n \n return [documents[idx] for idx in sorted_indices]\n\n\n2. Using Metadata for Filtering\nShortcut: Combine vector search with metadata filtering for precise results.\nimport chromadb\nfrom chromadb.config import Settings\nfrom sentence_transformers import SentenceTransformer\n\n# === Setup ===\nclient = chromadb.PersistentClient(path=\"./chroma_store\")\ncollection_name = \"document_search\"\n\n# Delete existing collection (optional)\ntry:\n client.delete_collection(collection_name)\nexcept:\n pass\n\ncollection = client.get_or_create_collection(name=collection_name)\n\n# === Model ===\nmodel = SentenceTransformer(\"all-MiniLM-L6-v2\")\n\n# === Documents and Metadata ===\ndocuments = [\n \"Machine learning tutorial\",\n \"Python programming\",\n \"Data science guide\"\n]\n\nmetadatas = [\n    {\"category\": \"AI\", \"difficulty\": \"beginner\", \"date\": \"2023-01-15\"},\n    {\"category\": \"Programming\", \"difficulty", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 4, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "ne learning tutorial\",\n \"Python programming\",\n \"Data science guide\"\n]\n\nmetadatas = [\n    {\"category\": \"AI\", \"difficulty\": \"beginner\", \"date\": \"2023-01-15\"},\n    {\"category\": \"Programming\", \"difficulty\": \"intermediate\", \"date\": \"2023-02-20\"},\n    {\"category\": \"Data\", \"difficulty\": \"advanced\", \"date\": \"2023-03-10\"}\n]\n\n# === Embeddings ===\nembeddings = model.encode(documents)\n\n# === Add to ChromaDB ===\ncollection.add(\n ids=[\"doc1\", \"doc2\", \"doc3\"],\n documents=documents,\n embeddings=[e.tolist() for e in embeddings],\n metadatas=metadatas\n)\n\nprint(\"✅ Documents added to collection.\")\n\n# === Query Example: Prepare query embedding ===\nquery_text = \"How to get started with AI\"\nquery_embedding = model.encode([query_text])[0].tolist()\n\n\n\n# === Query 1: Metadata filtering with AND ===\nprint(\"\\n🔍 Querying with metadata filter (category='AI' AND difficulty='beginner'):\")\nresults = collection.query(\n query_embeddings=[query_embedding],\n where={\n \"$and\": [\n            {\"category\": \"AI\"},\n            {\"", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 5, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "ng with metadata filter (category='AI' AND difficulty='beginner'):\")\nresults = collection.query(\n query_embeddings=[query_embedding],\n where={\n \"$and\": [\n            {\"category\": \"AI\"},\n            {\"difficulty\": \"beginner\"}\n        ]\n    },\n n_results=5\n)\nfor doc in results[\"documents\"][0]:\n print(f\"→ {doc}\")\n\n\n\n# === Query 2: Document content filtering ===\nprint(\"\\n🔍 Querying with document content filter (contains 'Python'):\")\nresults = collection.query(\n query_embeddings=[query_embedding],\n where_document={\"$contains\": \"Python\"},\n n_results=5\n)\nfor doc in results[\"documents\"][0]:\n print(f\"→ {doc}\")\n\n\nData Management Tips\n1. Document Preprocessing\nTip: Preprocess your documents for better embedding quality.\nimport spacy\n\nnlp = spacy.load(\"en_core_web_sm\")\n\ndef preprocess_text_spacy(text):\n doc = nlp(text.lower())\n tokens = [token.lemma_ for token in doc if not token.is_stop and token.is_alpha]\n return ' '.join(tokens)\n\n# Example\npreprocessed_docs = [preprocess_text_spacy(doc) for doc", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 6, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": ":\n doc = nlp(text.lower())\n tokens = [token.lemma_ for token in doc if not token.is_stop and token.is_alpha]\n return ' '.join(tokens)\n\n# Example\npreprocessed_docs = [preprocess_text_spacy(doc) for doc in documents]\n\nembeddings = model.encode(preprocessed_docs)\n\n\n2. Chunking Long Documents\nTrick: Split long documents into smaller chunks for better semantic search.\ndef chunk_document(document, chunk_size=200, overlap=50):\n \"\"\"Split a document into overlapping chunks.\"\"\"\n words = document.split()\n chunks = []\n \n for i in range(0, len(words), chunk_size - overlap):\n chunk = ' '.join(words[i:i + chunk_size])\n chunks.append(chunk)\n \n # Stop if we've reached the end of the document\n if i + chunk_size >= len(words):\n break\n \n return chunks\n\n# Process a collection of documents\ndocument_chunks = []\nchunk_metadata = []\nchunk_ids = []\n\nfor doc_id, document in enumerate(documents):\n # Get document chunks\n chunks = chunk_document(document)\n \n for chunk_num, chunk in enumerate(chunks):\n document_chun", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 7, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "chunk_metadata = []\nchunk_ids = []\n\nfor doc_id, document in enumerate(documents):\n # Get document chunks\n chunks = chunk_document(document)\n \n for chunk_num, chunk in enumerate(chunks):\n document_chunks.append(chunk)\n chunk_ids.append(f\"doc_{doc_id}_chunk_{chunk_num}\")\n chunk_metadata.append({\n \"doc_id\": doc_id,\n \"chunk_num\": chunk_num,\n \"total_chunks\": len(chunks)\n        })\n\n# Generate embeddings for chunks\nchunk_embeddings = model.encode(document_chunks).tolist()\n\n# Add to collection\ncollection.add(\n ids=chunk_ids,\n documents=document_chunks,\n embeddings=chunk_embeddings,\n metadatas=chunk_metadata\n)\n\n\n3. Handling Updates and Deletions\nShortcut: Efficiently manage document updates using unique IDs.\n# Update a document by ID\ndoc_id = \"doc_123\"\n\n# Check if document exists\nexisting_docs = collection.get(ids=[doc_id])\nif existing_docs[\"ids\"]:\n # Delete the existing document\n collection.delete(ids=[doc_id])\n \n # Add the updated document with the same ID\n updated_text = \"Updated document c", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 8, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "ollection.get(ids=[doc_id])\nif existing_docs[\"ids\"]:\n # Delete the existing document\n collection.delete(ids=[doc_id])\n \n # Add the updated document with the same ID\n updated_text = \"Updated document content\"\n updated_embedding = model.encode([updated_text])[0].tolist()\n collection.add(\n ids=[doc_id],\n documents=[updated_text],\n embeddings=[updated_embedding]\n    )\n print(f\"Document {doc_id} updated successfully\")\nelse:\n print(f\"Document {doc_id} not found\")\n\n\nEmbedding Strategies\n1. Choosing the Right Embedding Model\nTip: Select embedding models based on your specific use case.\n\n\nUse Case\nRecommended Models\n\n\nGeneral text search\nall-MiniLM-L6-v2, all-mpnet-base-v2\n\n\nMultilingual search\nparaphrase-multilingual-MiniLM-L12-v2\n\n\nCode search\ncodeberta-small-v2, codebert-base\n\n\nScientific papers\nspecter-base-v2\n\n\nLong document search\nmpnet-base with chunking strategy\n\n\n\nfrom sentence_transformers import SentenceTransformer\nimport chromadb \n\n# Initialize client (add this if not already presen", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 9, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "specter-base-v2\n\n\nLong document search\nmpnet-base with chunking strategy\n\n\n\nfrom sentence_transformers import SentenceTransformer\nimport chromadb \n\n# Initialize client (add this if not already present)\nclient = chromadb.Client()\n\n# Load models\ngeneral_model = SentenceTransformer('all-MiniLM-L6-v2')\n\n# Code collection\ncode_model = SentenceTransformer('microsoft/codebert-base')  \n\n# Multilingual collection\nmultilingual_model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')\n\n# Create collections\ngeneral_collection = client.create_collection(\"general_text\")\ncode_collection = client.create_collection(\"code_snippets\")\nmultilingual_collection = client.create_collection(\"multilingual_docs\")\n\n\n\n2. Custom Embedding Functions\nTrick: Create custom embedding functions for specialized domains.\nfrom sentence_transformers import SentenceTransformer\nimport torch\n\nclass CustomEmbedder:\n def __init__(self):\n self.model = SentenceTransformer('all-MiniLM-L6-v2')\n \n def embed_documents(self,", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 10, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "ains.\nfrom sentence_transformers import SentenceTransformer\nimport torch\n\nclass CustomEmbedder:\n def __init__(self):\n self.model = SentenceTransformer('all-MiniLM-L6-v2')\n \n def embed_documents(self, texts):\n # Custom preprocessing\n processed_texts = [self._preprocess(text) for text in texts]\n \n # Generate base embeddings\n embeddings = self.model.encode(processed_texts)\n \n # Apply custom post-processing (e.g., emphasize certain dimensions)\n for i, (text, embedding) in enumerate(zip(texts, embeddings)):\n if \"important_keyword\" in text.lower():\n # Boost certain dimensions for specific content\n embeddings[i][:10] *= 1.2\n \n # Normalize embedding after modification\n embeddings[i] = embeddings[i] / np.linalg.norm(embeddings[i])\n \n return embeddings\n \n def _preprocess(self, text):\n # Custom preprocessing logic\n return text.replace('shorthand', 'detailed explanation')\n\n# Use the custom embedder\ncustom_embedder = CustomEmbedder()\nembeddings = custom_embedder.embed_documents(documents)\n\n3. Handl", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 11, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "ocessing logic\n return text.replace('shorthand', 'detailed explanation')\n\n# Use the custom embedder\ncustom_embedder = CustomEmbedder()\nembeddings = custom_embedder.embed_documents(documents)\n\n3. Handling Multiple Languages\nShortcut: Implement language detection for multi-language collections.\nimport langid\nfrom sentence_transformers import SentenceTransformer\n\n# Load multilingual model\nmodel = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')\n\n# Function to detect language and process accordingly\ndef process_multilingual_document(document):\n # Detect language\n lang, confidence = langid.classify(document)\n \n # Add language information to metadata\n metadata = {\"detected_language\": lang, \"language_confidence\": confidence}\n \n # For very short texts in certain languages, you might want to augment\n if len(document) < 20 and lang in ['ja', 'zh', 'ko']:\n # For short CJK documents, duplicate content to improve embedding\n document = document + \" \" + document\n \n # Generate embedding\n e", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 12, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "nt to augment\n if len(document) < 20 and lang in ['ja', 'zh', 'ko']:\n # For short CJK documents, duplicate content to improve embedding\n document = document + \" \" + document\n \n # Generate embedding\n embedding = model.encode(document).tolist()\n \n return document, embedding, metadata\n\n# Process a batch of documents\nids = []\nprocessed_docs = []\nembeddings = []\nmetadatas = []\n\nfor i, doc in enumerate(documents):\n doc_id = f\"doc_{i}\"\n processed_doc, embedding, metadata = process_multilingual_document(doc)\n \n ids.append(doc_id)\n processed_docs.append(processed_doc)\n embeddings.append(embedding)\n metadatas.append(metadata)\n\n# Add to collection\ncollection.add(\n ids=ids,\n documents=processed_docs,\n embeddings=embeddings,\n metadatas=metadatas\n)\n\n\nDeployment and Production Tips\n1. Persistence Configuration\nTip: Configure persistence properly for production environments.\nuse path=\"/path/to/database\" properly\n\n2. Incremental Updates\nTrick: Implement an efficient incremental update strategy.\nimport", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 13, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "ration\nTip: Configure persistence properly for production environments.\nuse path=\"/path/to/database\" properly\n\n2. Incremental Updates\nTrick: Implement an efficient incremental update strategy.\nimport hashlib\nimport json\nfrom datetime import datetime\n\ndef get_document_hash(document):\n \"\"\"Generate a hash for document content to detect changes.\"\"\"\n return hashlib.md5(document.encode()).hexdigest()\n\ndef update_collection_incrementally(collection, documents, document_ids=None):\n \"\"\"Update collection with new or changed documents only.\"\"\"\n # If no IDs provided, generate them\n if document_ids is None:\n document_ids = [f\"doc_{i}\" for i in range(len(documents))]\n \n # Get existing documents and their hashes\n existing_docs = {}\n try:\n all_ids = collection.get()[\"ids\"]\n if all_ids:\n existing_data = collection.get(ids=all_ids)\n for i, doc_id in enumerate(existing_data[\"ids\"]):\n doc_content = existing_data[\"documents\"][i]\n existing_docs[doc_id] = {\n \"content\": doc_content,\n \"hash\": get_document_hash", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 14, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "llection.get(ids=all_ids)\n for i, doc_id in enumerate(existing_data[\"ids\"]):\n doc_content = existing_data[\"documents\"][i]\n existing_docs[doc_id] = {\n \"content\": doc_content,\n \"hash\": get_document_hash(doc_content)\n                }\n except Exception as e:\n print(f\"Error accessing collection: {e}\")\n \n # Identify new and changed documents\n new_ids = []\n new_docs = []\n new_metadatas = []\n \n for i, (doc_id, document) in enumerate(zip(document_ids, documents)):\n current_hash = get_document_hash(document)\n \n if doc_id not in existing_docs or existing_docs[doc_id][\"hash\"] != current_hash:\n new_ids.append(doc_id)\n new_docs.append(document)\n new_metadatas.append({\n \"updated_at\": datetime.now().isoformat(),\n \"content_hash\": current_hash\n            })\n \n # If there are new or changed documents, update the collection\n if new_docs:\n print(f\"Adding or updating {len(new_docs)} documents\")\n \n # Generate embeddings only for new/changed docs\n embeddings = model.encode(new_docs).tolist()\n \n # Remove exi", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 15, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "te the collection\n if new_docs:\n print(f\"Adding or updating {len(new_docs)} documents\")\n \n # Generate embeddings only for new/changed docs\n embeddings = model.encode(new_docs).tolist()\n \n # Remove existing docs that will be updated\n existing_ids = [doc_id for doc_id in new_ids if doc_id in existing_docs]\n if existing_ids:\n collection.delete(ids=existing_ids)\n \n # Add new and updated documents\n collection.add(\n ids=new_ids,\n documents=new_docs,\n embeddings=embeddings,\n metadatas=new_metadatas\n        )\n return len(new_docs)\n else:\n print(\"No new or updated documents found\")\n return 0\n\n\n3. Monitoring and Logging\nShortcut: Implement basic monitoring for Chroma DB operations.\nimport time\nimport logging\nfrom functools import wraps\nfrom sentence_transformers import SentenceTransformer\nimport chromadb\n\n# Configure logging\nlogging.basicConfig(\n level=logging.INFO,\n format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\n handlers=[\n logging.FileHandler(\"chroma_operations.log\"),\n logging", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 16, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "omadb\n\n# Configure logging\nlogging.basicConfig(\n level=logging.INFO,\n format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\n handlers=[\n logging.FileHandler(\"chroma_operations.log\"),\n logging.StreamHandler()\n    ]\n)\n\nlogger = logging.getLogger(\"chroma_db\")\n\n# Decorator for timing and logging operations\ndef monitor_operation(func):\n @wraps(func)\n def wrapper(*args, **kwargs):\n start_time = time.time()\n operation_name = func.__name__\n \n logger.info(f\"Starting operation: {operation_name}\")\n \n try:\n result = func(*args, **kwargs)\n execution_time = time.time() - start_time\n \n logger.info(f\"Operation {operation_name} completed in {execution_time:.4f} seconds\")\n \n # Additional logging for specific operations\n if operation_name == \"add\":\n doc_count = len(kwargs.get(\"documents\", []))\n logger.info(f\"Added {doc_count} documents to collection\")\n elif operation_name == \"query\":\n n_results = kwargs.get(\"n_results\", 0)\n logger.info(f\"Queried for {n_results} results\")\n \n return result\n \n exce", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 17, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "er.info(f\"Added {doc_count} documents to collection\")\n elif operation_name == \"query\":\n n_results = kwargs.get(\"n_results\", 0)\n logger.info(f\"Queried for {n_results} results\")\n \n return result\n \n except Exception as e:\n logger.error(f\"Error in operation {operation_name}: {str(e)}\")\n raise\n return wrapper\n\n# Monitored collection wrapper\nclass MonitoredCollection:\n def __init__(self, collection):\n self.collection = collection\n \n @monitor_operation\n def add(self, *args, **kwargs):\n return self.collection.add(*args, **kwargs)\n \n @monitor_operation\n def query(self, *args, **kwargs):\n return self.collection.query(*args, **kwargs)\n \n @monitor_operation\n def delete(self, *args, **kwargs):\n return self.collection.delete(*args, **kwargs)\n \n def __getattr__(self, attr):\n return getattr(self.collection, attr)\n\n# Initialize Chroma client and create collection\nclient = chromadb.Client()\nclient.delete_collection(\"my_collection\")\ncollection = client.create_collection(\"my_collection\")\nmonitored_collect", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 18, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "n, attr)\n\n# Initialize Chroma client and create collection\nclient = chromadb.Client()\nclient.delete_collection(\"my_collection\")\ncollection = client.create_collection(\"my_collection\")\nmonitored_collection = MonitoredCollection(collection)\n\n# Load embedding model\nmodel = SentenceTransformer('all-MiniLM-L6-v2')\n\n# Example documents\ndocuments = [\n \"Machine learning is fascinating.\",\n \"Natural language processing with transformers.\",\n \"Embedding vectors enable semantic search.\"\n]\n\n# Generate embeddings\nembeddings = model.encode(documents).tolist()\n\n# Generate string IDs\nids = [f\"doc_{i}\" for i in range(len(documents))]\n\n# Add documents to collection with logging\nmonitored_collection.add(\n ids=ids,\n documents=documents,\n embeddings=embeddings\n)\n\n# Query example with logging\nquery_text = \"machine learning\"\nquery_embedding = model.encode([query_text]).tolist()\n\nresults = monitored_collection.query(\n query_embeddings=query_embedding,\n n_results=2\n)\n\nprint(results)\n\n\n\nQuick Reference: Common Ope", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 19, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "learning\"\nquery_embedding = model.encode([query_text]).tolist()\n\nresults = monitored_collection.query(\n query_embeddings=query_embedding,\n n_results=2\n)\n\nprint(results)\n\n\n\nQuick Reference: Common Operations\nCollection Management\n# Create a collection\ncollection = client.create_collection(\"my_collection\")\n\n# List all collections\ncollections = client.list_collections()\n\n# Get a collection\ncollection = client.get_collection(\"my_collection\")\n\n# Delete a collection\nclient.delete_collection(\"my_collection\")\n\nDocument Operations\n# Add documents\ncollection.add(\n ids=[\"id1\", \"id2\"],\n documents=[\"document 1\", \"document 2\"],\n embeddings=[[0.1, 0.2, 0.3], [0.3, 0.4, 0.5]],  # Use actual numeric lists, no ellipsis\n metadatas=[{\"key\": \"value\"}, {\"key\": \"value2\"}]  # Optional\n)\n\n# Get documents\ndocs = collection.get(\n ids=[\"id1\", \"id2\"],  # Optional\n where={\"key\": \"value\"},  # Optional\n limit=10,  # Optional\n offset=0 # Optional\n)\n\n# Update documents\ncollection.update(\n ids=[\"id1\"],\n documents=[\"upd", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 20, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "ollection.get(\n ids=[\"id1\", \"id2\"],  # Optional\n where={\"key\": \"value\"},  # Optional\n limit=10,  # Optional\n offset=0 # Optional\n)\n\n# Update documents\ncollection.update(\n ids=[\"id1\"],\n documents=[\"updated document\"],\n embeddings=[[0.5, 0.6, 0.7]],  # Use actual numeric lists\n metadatas=[{\"key\": \"new_value\"}]  # Optional\n)\n\n# Delete documents\ncollection.delete(\n ids=[\"id1\", \"id2\"],  # Optional\n where={\"key\": \"value\"}  # Optional\n)\n\n\n\nQuery Operations\nprint(\"\\n🔍 Querying with document content filter (contains 'Python'):\")\nresults = collection.query(\n query_embeddings=[query_embedding],\n where_document={\"$contains\": \"Python\"},\n n_results=5\n)\nfor doc in results[\"documents\"][0]:\n print(f\"→ {doc}\")\n\nChroma DB Environment Variables\n\n\nVariable Name\nDescription\nDefault\n\n\nCHROMA_TELEMETRY_ENABLED\nEnable/disable anonymous telemetry\ntrue\n\n\nCHROMA_DB_IMPL\nBackend implementation\nduckdb+parquet\n\n\nCHROMA_SERVER_HOST\nHost for Chroma server\nlocalhost\n\n\nCHROMA_SERVER_PORT\nPort for Chroma server\n8000\n\n\nCH", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 21, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "able/disable anonymous telemetry\ntrue\n\n\nCHROMA_DB_IMPL\nBackend implementation\nduckdb+parquet\n\n\nCHROMA_SERVER_HOST\nHost for Chroma server\nlocalhost\n\n\nCHROMA_SERVER_PORT\nPort for Chroma server\n8000\n\n\nCHROMA_SERVER_HTTP_PORT\nHTTP port for Chroma server\n8000\n\n\nCHROMA_PERSIST_DIRECTORY\nDirectory for persistence\n.chromadb/\n\n\nCHROMA_API_IMPL\nAPI implementation\nlocal\n\n\nCHROMA_API_HOST\nAPI host\nlocalhost\n\n\nCHROMA_API_PORT\nAPI port\n8000\n\n\n\nCommon Troubleshooting\n\n\nIssue\nSolution\n\n\nModuleNotFoundError: No module named 'chromadb'\nReinstall with pip install chromadb or check Python environment\n\n\nValueError: Embeddings not provided and no embedding function found\nProvide embeddings or set an embedding function for the collection\n\n\nOut of memory errors with large collections\nUse disk-based persistence and batch processing\n\n\nSlow query performance\nReduce dimensionality, optimize index settings, or use approximate nearest neighbors\n\n\nRuntimeError: Expected all tensors to be on the same device\nEnsure al", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 22, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "d batch processing\n\n\nSlow query performance\nReduce dimensionality, optimize index settings, or use approximate nearest neighbors\n\n\nRuntimeError: Expected all tensors to be on the same device\nEnsure all tensor operations use consistent devices (CPU/GPU)\n\n\nEmbedding dimension mismatch\nEnsure all embeddings in a collection have the same dimensions\n\n\nKeyboard Shortcuts for Notebooks\nWhen working with Chroma DB in Jupyter notebooks:\n\n\nShortcut\nAction\n\n\nShift+Enter\nRun cell and select below\n\n\nCtrl+Enter\nRun cell\n\n\nAlt+Enter\nRun cell and insert below\n\n\nEsc then A\nInsert cell above\n\n\nEsc then B\nInsert cell below\n\n\nEsc then D,D\nDelete cell\n\n\nEsc then Z\nUndo cell deletion\n\n\nEsc then M\nConvert cell to Markdown (for documentation)\n\n\nEsc then Y\nConvert cell to code\n\n\nEsc then L\nToggle line numbers (useful for debugging)", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 23, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "ful for debugging)", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 24, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C21dd33dc-e79e-734b-a302-7223f93c86a4%2F%29", "section_group": "Vector DBs"}}, {"text": "🧩 Use Cases and Example Code\n\n\n\n\n\n\n\n\nChroma DB excels in a variety of scenarios where vector embeddings and semantic search are valuable. Below are key use cases with corresponding example code to demonstrate implementation.\n\n1. Semantic Document Search\nUse Case Overview\nCreate a system that understands the meaning behind search queries rather than relying on exact keyword matches. This allows users to find documents based on concepts and ideas, even when specific terms aren't present.\nExample Code\nimport chromadb\nimport pandas as pd\nfrom sentence_transformers import SentenceTransformer\n\n# Initialize client and model\nclient = chromadb.Client()\nmodel = SentenceTransformer(\"all-MiniLM-L6-v2\")\n\n# Create a collection for documents\ncollection = client.create_collection(\"document_search\")\n\n# Sample documents\ndocuments = [\n \"Artificial intelligence is revolutionizing healthcare with predictive analytics\",\n \"Machine learning algorithms can process medical images to detect anomalies\",\n \"Data pr", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "ple documents\ndocuments = [\n \"Artificial intelligence is revolutionizing healthcare with predictive analytics\",\n \"Machine learning algorithms can process medical images to detect anomalies\",\n \"Data privacy concerns are growing as more health information becomes digital\",\n \"Electronic health records improve patient care coordination between providers\",\n \"Telehealth services expanded dramatically during the global pandemic\"\n]\n\n# Generate embeddings and add to collection\nembeddings = model.encode(documents)\nfor i, (doc, embedding) in enumerate(zip(documents, embeddings)):\n collection.add(\n ids=[f\"doc_{i}\"],\n documents=[doc],\n embeddings=[embedding.tolist()]\n    )\n\n# Perform semantic search\nquery = \"AI applications in medicine\"\nquery_embedding = model.encode([query]).tolist()\nresults = collection.query(\n query_embeddings=query_embedding,\n n_results=3\n)\n\nprint(\"Query:\", query)\nprint(\"Top matches:\")\nfor i, doc in enumerate(results[\"documents\"][0]):\n print(f\"{i+1}. {doc}\")\n\n\n2. Product Recomm", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "ion.query(\n query_embeddings=query_embedding,\n n_results=3\n)\n\nprint(\"Query:\", query)\nprint(\"Top matches:\")\nfor i, doc in enumerate(results[\"documents\"][0]):\n print(f\"{i+1}. {doc}\")\n\n\n2. Product Recommendation System\nUse Case Overview\nBuild a recommendation engine that suggests products based on similarities between item descriptions, features, or user interactions, enabling more personalized customer experiences.\nimport chromadb\nfrom sentence_transformers import SentenceTransformer\n\n# === Config ===\nDELETE_EXISTING_COLLECTION = True\nCOLLECTION_NAME = \"product_recommendations\"\nSTORAGE_PATH = \"./chroma_store\"\n\nclient = chromadb.PersistentClient(path=STORAGE_PATH)\n\nif DELETE_EXISTING_COLLECTION:\n try:\n client.delete_collection(COLLECTION_NAME)\n print(f\"Deleted existing collection '{COLLECTION_NAME}'\")\n except Exception as e:\n print(f\"Collection did not exist or could not be deleted: {e}\")\n\ncollection = client.get_or_create_collection(COLLECTION_NAME)\nmodel = SentenceTransformer(\"all-MiniL", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "_NAME}'\")\n except Exception as e:\n print(f\"Collection did not exist or could not be deleted: {e}\")\n\ncollection = client.get_or_create_collection(COLLECTION_NAME)\nmodel = SentenceTransformer(\"all-MiniLM-L6-v2\")\n\nproducts = {\n \"prod_001\": {\"name\": \"Lightweight Running Shoes\", \"description\": \"Breathable mesh upper with responsive cushioning for daily runs\"},\n \"prod_002\": {\"name\": \"Trail Running Shoes\", \"description\": \"Durable outsole with rock protection plate for rough terrain\"},\n \"prod_003\": {\"name\": \"Wireless Earbuds\", \"description\": \"Bluetooth earphones with noise cancellation for workouts\"},\n \"prod_004\": {\"name\": \"Fitness Tracker\", \"description\": \"Wearable device that monitors steps, heart rate and sleep patterns\"},\n \"prod_005\": {\"name\": \"Running Socks\", \"description\": \"Moisture-wicking fabric with arch support for runners\"},\n}\n\ndef add_products():\n ids = list(products.keys())\n descriptions = [p[\"description\"] for p in products.values()]\n names = [p[\"name\"] for p in products.values()", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "fabric with arch support for runners\"},\n}\n\ndef add_products():\n ids = list(products.keys())\n descriptions = [p[\"description\"] for p in products.values()]\n names = [p[\"name\"] for p in products.values()]\n\n embeddings = model.encode(descriptions)\n\n for i, (prod_id, embedding) in enumerate(zip(ids, embeddings)):\n collection.add(\n ids=[prod_id],\n documents=[descriptions[i]],\n embeddings=[embedding.tolist()],\n metadatas=[{\"name\": names[i]}]\n        )\n print(\"Data added successfully!\")\n\ndef get_similar_products(product_id, n=2):\n results = collection.get(ids=[product_id], include=[\"embeddings\", \"documents\", \"metadatas\"])\n\n if results[\"embeddings\"] is None or len(results[\"embeddings\"]) == 0:\n print(f\"No embeddings found for product ID: {product_id}\")\n return []\n\n product_embedding = results[\"embeddings\"][0]\n\n similar = collection.query(\n query_embeddings=[product_embedding],\n n_results=n + 1\n    )\n\n similar_products = []\n for i, id_val in enumerate(similar[\"ids\"][0]):\n if id_val != product_id:", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 4, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "dings\"][0]\n\n similar = collection.query(\n query_embeddings=[product_embedding],\n n_results=n + 1\n    )\n\n similar_products = []\n for i, id_val in enumerate(similar[\"ids\"][0]):\n if id_val != product_id:\n similar_products.append({\n \"id\": id_val,\n \"name\": similar[\"metadatas\"][0][i][\"name\"],\n \"description\": similar[\"documents\"][0][i]\n            })\n return similar_products\n\nif __name__ == \"__main__\":\n if DELETE_EXISTING_COLLECTION:\n add_products()\n\n target_product = \"prod_001\"\n print(f\"\\nFinding products similar to: {products[target_product]['name']}\")\n similar_items = get_similar_products(target_product)\n\n for i, item in enumerate(similar_items):\n print(f\"{i+1}. {item['name']}: {item['description']}\")\n\n\n\n3. Question-Answering System\nUse Case Overview\nCreate a system that can answer questions by retrieving the most relevant passages from a knowledge base, making information retrieval more intuitive and contextual.\nExample Code\nimport chromadb\nimport pandas as pd\nfrom sentence_transformers i", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 5, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "y retrieving the most relevant passages from a knowledge base, making information retrieval more intuitive and contextual.\nExample Code\nimport chromadb\nimport pandas as pd\nfrom sentence_transformers import SentenceTransformer\n\n# Initialize client and model\nclient = chromadb.Client()\nmodel = SentenceTransformer(\"all-MiniLM-L6-v2\")\n\n# Create a collection for the knowledge base\ncollection = client.create_collection(\"knowledge_base\")\n\n# Sample knowledge base with facts\nknowledge_base = [\n \"The Pacific Ocean is the largest and deepest ocean on Earth, covering more than 60 million square miles.\",\n \"Mount Everest is the highest mountain above sea level, with a peak at 29,032 feet (8,849 meters).\",\n \"The Amazon Rainforest produces about 20% of the world's oxygen and is home to 10% of the known species on Earth.\",\n \"The Great Barrier Reef is the world's largest coral reef system, composed of over 2,900 individual reefs.\",\n \"The Sahara Desert is the largest hot desert in the world, covering abou", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 6, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "cies on Earth.\",\n \"The Great Barrier Reef is the world's largest coral reef system, composed of over 2,900 individual reefs.\",\n \"The Sahara Desert is the largest hot desert in the world, covering about 3.6 million square miles across North Africa.\",\n \"The human brain contains approximately 86 billion neurons and can generate about 23 watts of power.\",\n \"DNA, or deoxyribonucleic acid, contains the genetic instructions for the development and function of living organisms.\"\n]\n\n# Generate embeddings and add to collection\nembeddings = model.encode(knowledge_base)\nfor i, (fact, embedding) in enumerate(zip(knowledge_base, embeddings)):\n collection.add(\n ids=[f\"fact_{i}\"],\n documents=[fact],\n embeddings=[embedding.tolist()]\n    )\n\n# Function to answer questions\ndef answer_question(question):\n # Encode the question\n question_embedding = model.encode([question]).tolist()\n \n # Retrieve relevant facts\n results = collection.query(\n query_embeddings=question_embedding,\n n_results=2\n    )\n \n # Return", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 7, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "code the question\n question_embedding = model.encode([question]).tolist()\n \n # Retrieve relevant facts\n results = collection.query(\n query_embeddings=question_embedding,\n n_results=2\n    )\n \n # Return the most relevant facts\n return results[\"documents\"][0]\n\n# Example questions\nquestions = [\n \"What is the biggest ocean?\",\n \"Tell me about Mount Everest\",\n \"How many neurons are in the human brain?\"\n]\n\nfor question in questions:\n print(f\"Question: {question}\")\n answers = answer_question(question)\n print(\"Relevant information:\")\n for i, answer in enumerate(answers):\n print(f\"{i+1}. {answer}\")\n print()\n\n\n4. Image Search by Text Description\nUse Case Overview\nEnable searching for images using natural language descriptions by encoding both images and text into the same embedding space, allowing for cross-modal retrieval.\nExample Code\nimport chromadb\nimport numpy as np\nfrom sentence_transformers import SentenceTransformer\nfrom PIL import Image # You would need actual images to use this in practi", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 8, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "cross-modal retrieval.\nExample Code\nimport chromadb\nimport numpy as np\nfrom sentence_transformers import SentenceTransformer\nfrom PIL import Image # You would need actual images to use this in practice\n\n# Initialize client and model\n# Note: In a real implementation, you would use a multi-modal model like CLIP\n# This example uses sentence-transformers for simplicity\nclient = chromadb.Client()\nmodel = SentenceTransformer(\"all-MiniLM-L6-v2\")\n\n# Create a collection for images\ncollection = client.create_collection(\"image_search\")\n\n# Sample image descriptions (in a real scenario, these would be derived from image features)\nimage_descriptions = [\n \"A scenic mountain landscape with snow-capped peaks and clear blue sky\",\n \"A busy city street at night with colorful neon signs and traffic\",\n \"A serene beach with palm trees and turquoise water\",\n \"A dense forest with tall pine trees and sunlight filtering through\",\n \"A modern kitchen with stainless steel appliances and marble countertops\",\n \"A vi", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 9, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "ne beach with palm trees and turquoise water\",\n \"A dense forest with tall pine trees and sunlight filtering through\",\n \"A modern kitchen with stainless steel appliances and marble countertops\",\n \"A vintage car parked on a cobblestone street in an old European town\"\n]\n\n# In this example, we're using the descriptions as proxies for actual image embeddings\n# In a real application, you would use a proper image encoding model\nembeddings = model.encode(image_descriptions)\n\n# Add \"images\" to collection\nfor i, (desc, embedding) in enumerate(zip(image_descriptions, embeddings)):\n collection.add(\n ids=[f\"image_{i}\"],\n documents=[desc],  # In real implementation, this could store image path or metadata\n embeddings=[embedding.tolist()],\n metadatas=[{\"description\": desc}]\n    )\n\n# Function to search for images by text description\ndef search_images_by_text(text_query, n=3):\n # Encode the text query\n query_embedding = model.encode([text_query]).tolist()\n \n # Find similar images\n results = collection.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 10, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "r images by text description\ndef search_images_by_text(text_query, n=3):\n # Encode the text query\n query_embedding = model.encode([text_query]).tolist()\n \n # Find similar images\n results = collection.query(\n query_embeddings=query_embedding,\n n_results=n\n    )\n \n return results[\"metadatas\"][0]\n\n# Example text queries\ntext_queries = [\n \"mountains with snow\",\n \"urban cityscape\",\n \"nature scene with trees\"\n]\n\nfor query in text_queries:\n print(f\"Query: '{query}'\")\n matches = search_images_by_text(query)\n print(\"Matching images:\")\n for i, match in enumerate(matches):\n print(f\"{i+1}. {match['description']}\")\n print()\n\n\n\n5. Duplicate Content Detection\nUse Case Overview\nIdentify similar or duplicate content in large datasets, useful for removing redundancies in document collections or detecting plagiarism.\nExample Code\nimport chromadb\nimport numpy as np\nfrom sentence_transformers import SentenceTransformer\n\n# Initialize client and model (use PersistentClient for persistence)\nclient = chromadb.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 11, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "agiarism.\nExample Code\nimport chromadb\nimport numpy as np\nfrom sentence_transformers import SentenceTransformer\n\n# Initialize client and model (use PersistentClient for persistence)\nclient = chromadb.PersistentClient(path=\"./chroma_store\")  # PersistentClient to store data on disk\nmodel = SentenceTransformer(\"all-MiniLM-L6-v2\")\n\n# Create or get collection for content deduplication\ncollection = client.get_or_create_collection(\"content_deduplication\")\n\n# Sample content documents\ndocuments = [\n \"Machine learning algorithms find patterns in data to make predictions\",\n \"AI systems use machine learning to identify patterns and predict outcomes\",\n \"Climate change is causing global temperatures to rise significantly\",\n \"The increasing global temperature is a direct result of climate change\",\n \"Renewable energy sources include solar, wind, and hydroelectric power\",\n \"Regular exercise and a balanced diet are essential for good health\"\n]\n\n# Clear existing documents (optional)\ntry:\n client.delete_", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 12, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "ewable energy sources include solar, wind, and hydroelectric power\",\n \"Regular exercise and a balanced diet are essential for good health\"\n]\n\n# Clear existing documents (optional)\ntry:\n client.delete_collection(\"content_deduplication\")\n collection = client.get_or_create_collection(\"content_deduplication\")\nexcept Exception:\n pass\n\n# Generate embeddings\nembeddings = model.encode(documents)\n\n# Add documents to collection\nfor i, (doc, embedding) in enumerate(zip(documents, embeddings)):\n collection.add(\n ids=[f\"doc_{i}\"],\n documents=[doc],\n embeddings=[embedding.tolist()]\n    )\n\n# Function to find similar documents based on cosine similarity threshold\ndef find_similar_documents(similarity_threshold=0.85):\n similar_pairs = []\n \n for i in range(len(documents)):\n # Retrieve embedding for document i explicitly\n results = collection.get(ids=[f\"doc_{i}\"], include=[\"embeddings\"])\n embeddings_list = results.get(\"embeddings\")\n \n if embeddings_list is None or len(embeddings_list) == 0:\n print(f\"No e", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 13, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "i explicitly\n results = collection.get(ids=[f\"doc_{i}\"], include=[\"embeddings\"])\n embeddings_list = results.get(\"embeddings\")\n \n if embeddings_list is None or len(embeddings_list) == 0:\n print(f\"No embedding found for doc_{i}\")\n continue\n \n doc_embedding = embeddings_list[0]\n \n # Query collection for similar docs using this embedding\n results = collection.query(\n query_embeddings=[doc_embedding],\n n_results=len(documents)\n        )\n \n # Check cosine similarity for each retrieved doc\n for j, doc_id in enumerate(results[\"ids\"][0]):\n target_idx = int(doc_id.split(\"_\")[1])\n if target_idx <= i:\n continue # avoid duplicate pairs & self comparison\n \n sim = np.dot(embeddings[i], embeddings[target_idx]) / (\n np.linalg.norm(embeddings[i]) * np.linalg.norm(embeddings[target_idx])\n            )\n \n if sim >= similarity_threshold:\n similar_pairs.append({\n \"doc1_id\": f\"doc_{i}\",\n \"doc2_id\": f\"doc_{target_idx}\",\n \"doc1_text\": documents[i],\n \"doc2_text\": documents[target_idx],\n \"similarity\": sim", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 14, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "sim >= similarity_threshold:\n similar_pairs.append({\n \"doc1_id\": f\"doc_{i}\",\n \"doc2_id\": f\"doc_{target_idx}\",\n \"doc1_text\": documents[i],\n \"doc2_text\": documents[target_idx],\n \"similarity\": sim\n                })\n return similar_pairs\n\n\n\n\n# Run and print results\nsimilar_docs = find_similar_documents()\n\nprint(\"Potential duplicate content:\")\nfor idx, pair in enumerate(similar_docs):\n print(f\"Pair {idx+1} - Similarity: {pair['similarity']:.4f}\")\n print(f\"  Doc 1: {pair['doc1_text']}\")\n print(f\"  Doc 2: {pair['doc2_text']}\\n\")\n\n\n\n6. Multi-Lingual Information Retrieval\nUse Case Overview\nEnable searching across content in multiple languages by using language-agnostic embeddings that capture semantic meaning regardless of the language.\nExample Code\nimport chromadb\nfrom sentence_transformers import SentenceTransformer\n\n# Initialize client and model\n# Note: Use a multilingual model for actual implementation\nclient = chromadb.Client()\nmodel = SentenceTransformer(\"paraphrase-multilingual-MiniLM-", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 15, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "rt SentenceTransformer\n\n# Initialize client and model\n# Note: Use a multilingual model for actual implementation\nclient = chromadb.Client()\nmodel = SentenceTransformer(\"paraphrase-multilingual-MiniLM-L12-v2\")\n\n# Create a collection for multilingual content\ncollection = client.create_collection(\"multilingual_content\")\n\n# Sample content in different languages\nmultilingual_docs = [\n    {\"text\": \"Machine learning enables computers to learn from data\", \"language\": \"English\"},\n    {\"text\": \"El aprendizaje automático permite a las computadoras aprender de los datos\", \"language\": \"Spanish\"},\n    {\"text\": \"機械学習はコンピュータがデータから学ぶことを可能にします\", \"language\": \"Japanese\"},\n    {\"text\": \"L'apprentissage automatique permet aux ordinateurs d'apprendre à partir de données\", \"language\": \"French\"},\n    {\"text\": \"Maschinelles Lernen ermöglicht es Computern, aus Daten zu lernen\", \"language\": \"German\"},\n    {\"text\": \"Artificial intelligence is transforming many industries\", \"language\": \"English\"},\n    {\"text\": \"La", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 16, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "chinelles Lernen ermöglicht es Computern, aus Daten zu lernen\", \"language\": \"German\"},\n    {\"text\": \"Artificial intelligence is transforming many industries\", \"language\": \"English\"},\n    {\"text\": \"La inteligencia artificial está transformando muchas industrias\", \"language\": \"Spanish\"}\n]\n\n# Generate embeddings and add to collection\nfor i, doc in enumerate(multilingual_docs):\n embedding = model.encode(doc[\"text\"]).tolist()\n collection.add(\n ids=[f\"doc_{i}\"],\n documents=[doc[\"text\"]],\n embeddings=[embedding],\n metadatas=[{\"language\": doc[\"language\"]}]\n    )\n\n# Function to search across languages\ndef multilingual_search(query, query_language, n=3):\n # Encode the query\n query_embedding = model.encode(query).tolist()\n \n # Retrieve relevant documents in any language\n results = collection.query(\n query_embeddings=[query_embedding],\n n_results=n\n    )\n \n # Format results\n search_results = []\n for i in range(len(results[\"documents\"][0])):\n search_results.append({\n \"text\": results[\"documents\"][0]", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 17, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "query_embeddings=[query_embedding],\n n_results=n\n    )\n \n # Format results\n search_results = []\n for i in range(len(results[\"documents\"][0])):\n search_results.append({\n \"text\": results[\"documents\"][0][i],\n \"language\": results[\"metadatas\"][0][i][\"language\"]\n        })\n \n return search_results\n\n# Example queries in different languages\nqueries = [\n    {\"text\": \"How does machine learning work?\", \"language\": \"English\"},\n    {\"text\": \"¿Qué es la inteligencia artificial?\", \"language\": \"Spanish\"}\n]\n\nfor query in queries:\n print(f\"Query ({query['language']}): '{query['text']}'\")\n results = multilingual_search(query[\"text\"], query[\"language\"])\n print(\"Results:\")\n for i, result in enumerate(results):\n print(f\"{i+1}. [{result['language']}] {result['text']}\")\n print()\n\n\nBest Practices for Implementation\nWhen implementing these use cases with Chroma DB, consider the following best practices:\n\nChoose the Right Embedding Model: Select an embedding model that aligns with your specific use case. For gen", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 18, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "n\nWhen implementing these use cases with Chroma DB, consider the following best practices:\n\nChoose the Right Embedding Model: Select an embedding model that aligns with your specific use case. For general text applications, models like \"all-MiniLM-L6-v2\" work well, but specialized tasks may benefit from domain-specific models.\nOptimize Collection Structure: Organize your data into logical collections based on content type or application domain.\nIndex Performance Considerations:\n\nFor larger datasets (>100K embeddings), consider using HNSW or other efficient indexing methods\nAdd data in batches rather than individually for better performance\nSet appropriate n_results values to avoid unnecessary computation\n\n\nMetadata Usage: Leverage metadata effectively to store additional information that can help filter or enrich search results without affecting vector similarity.\nDistance Metrics: Choose appropriate distance metrics for your application:\n\nCosine similarity for text embeddings\nEuclidea", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 19, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "an help filter or enrich search results without affecting vector similarity.\nDistance Metrics: Choose appropriate distance metrics for your application:\n\nCosine similarity for text embeddings\nEuclidean distance for some visual and audio embeddings\n\n\nPersistence Configuration: Configure persistent storage for production applications to prevent data loss:\npython\nclient = chromadb.PersistentClient(path=\"/path/to/storage\")\nQuery Filtering: Use metadata filters to narrow down search results:\npython\nresults = collection.query(￼    query_embeddings=[query_embedding],￼    where={\"category\": \"technology\"},￼    n_results=5￼)\nRegular Maintenance: Implement strategies for managing collection growth:\n\nPeriodically remove outdated or irrelevant entries\nConsider reindexing as collection size increases significantly", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 20, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "gnificantly", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 21, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Ca1c37690-9596-974a-88e0-5e16d65e8ae1%2F%29", "section_group": "Vector DBs"}}, {"text": "🔧 Setup and Configuration\n\n\n\n\n\nIntroduction\nChroma DB is an open-source embedding database ideal for managing large vector datasets in machine learning applications. This guide provides clear instructions for setting up and configuring Chroma DB on both Windows and macOS, emphasizing best practices for implementation.\n\nKey Points\n\nChroma DB setup requires Python 3.9+, pip, and a virtual environment for best practice\nInstallation is simple: use pip install chromadb on both Windows and macOS\nConfiguration involves setting a data storage path, defaulting to ./chroma\nVerify setup by running a test script to ensure the client initializes correctly\nThe process is straightforward, with clear steps for both operating systems\n\n\nPrerequisites\nBefore proceeding, ensure the following are in place:\n\n\nRequirement\nDetails\n\n\nPython\nVersion 3.9+ required, verify with python --version or python3 --version\n\n\npip\nUpdate with pip install --upgrade pip\n\n\nVirtual Environment\nRecommended, use venv or conda fo", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cc51ad14b-d65e-d943-aa6c-73cd5de52830%2F%29", "section_group": "Vector DBs"}}, {"text": "equirement\nDetails\n\n\nPython\nVersion 3.9+ required, verify with python --version or python3 --version\n\n\npip\nUpdate with pip install --upgrade pip\n\n\nVirtual Environment\nRecommended, use venv or conda for isolation\n\n\nEnsure you have Python 3.9 or later installed, as Chroma DB requires this version. Update pip with pip install --upgrade pip and consider using a virtual environment (like venv) to isolate dependencies, which helps avoid conflicts.\n\nSetting Up a Virtual Environment\nCreating a virtual environment ensures a clean, isolated environment for Chroma DB:\nFor Windows:\n\nOpen Command Prompt and navigate to your project directory: cd path\\to\\your\\project\nCreate a virtual environment: python -m venv chroma_env\nActivate it: chroma_env\\Scripts\\activate \n\nYou'll see (chroma_env) in your prompt, indicating activation\n\n\n\nFor macOS:\n\nOpen Terminal and navigate to your project directory: cd /path/to/your/project\nCreate a virtual environment: python3 -m venv chroma_env\nActivate it: source chroma", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🔧 Setup and Configuration", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cc51ad14b-d65e-d943-aa6c-73cd5de52830%2F%29", "section_group": "Vector DBs"}}, {"text": "indicating activation\n\n\n\nFor macOS:\n\nOpen Terminal and navigate to your project directory: cd /path/to/your/project\nCreate a virtual environment: python3 -m venv chroma_env\nActivate it: source chroma_env/bin/activate \n\nYou'll see (chroma_env) in your prompt, indicating activation\n\n\n\nThis step ensures that dependencies for Chroma DB don't interfere with other projects.\n\nInstallation Steps\nWith your virtual environment activated, install Chroma DB using pip:\npip install chromadb\nThis command installs Chroma DB and all necessary dependencies. For specific versions or from GitHub, you can use:\n\npip install chromadb==<x.y.z> for a specific version\npip install git+https://github.com/chroma-core/chroma.git@main for the latest development version\n\n\nVerification\nTo ensure Chroma DB is installed correctly, create a simple Python script to test the client initialization:\n\nCreate test_chroma.py with the following content:\n\nimport chromadb￼\n￼client = chromadb.Client()￼\nprint(\"Chroma DB client crea", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🔧 Setup and Configuration", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cc51ad14b-d65e-d943-aa6c-73cd5de52830%2F%29", "section_group": "Vector DBs"}}, {"text": "rrectly, create a simple Python script to test the client initialization:\n\nCreate test_chroma.py with the following content:\n\nimport chromadb￼\n￼client = chromadb.Client()￼\nprint(\"Chroma DB client created successfully!\")\n\nRun it: \n\nWindows: python test_chroma.py\nmacOS: python3 test_chroma.py\n\n\n\nIf the output is \"Chroma DB client created successfully!\", your installation is verified.\n\nBasic Configuration\nChroma DB stores data in a chroma directory in the current working directory by default. However, you can specify a custom path for data persistence:\nWhen creating the client, use the path parameter, for example:\n\nWindows: client = chromadb.Client(path=\"C:\\\\path\\\\to\\\\your\\\\database\")\nmacOS: client = chromadb.Client(path=\"/path/to/database\")\n\nEnsure the directory exists and you have write permissions. Note the difference in path separators: Windows uses backslashes (\\), while macOS uses forward slashes (/).\n\nBasic Usage\nBelow is a practical example of using Chroma DB, focusing on creating", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🔧 Setup and Configuration", "chunk_index": 3, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cc51ad14b-d65e-d943-aa6c-73cd5de52830%2F%29", "section_group": "Vector DBs"}}, {"text": "missions. Note the difference in path separators: Windows uses backslashes (\\), while macOS uses forward slashes (/).\n\nBasic Usage\nBelow is a practical example of using Chroma DB, focusing on creating collections, adding documents, and querying embeddings:\nimport chromadb\nimport pandas as pd\nfrom sentence_transformers import SentenceTransformer\n \n# Initialize Chroma DB client and create a collection\nclient = chromadb.Client()\ncollection = client.create_collection(\"my_collection\")\n \n# Load sample data (adjust path for your OS)\ndata = pd.read_excel(r\"C:\\path\\to\\data.xlsx\")  # Windows\n# data = pd.read_excel(\"/path/to/data.xlsx\")   # macOS\n \n# Initialize embedding model\nmodel = SentenceTransformer(\"all-MiniLM-L6-v2\")\n \n# Generate embeddings for descriptions\nembeddings = model.encode(data['Description'].values)\n \n# Add data to Chroma DB\nfor idx, embedding in enumerate(embeddings):\n    collection.add(\n        ids=[str(idx)],\n        documents=[data['Description'].iloc[idx]],\n        embeddin", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🔧 Setup and Configuration", "chunk_index": 4, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cc51ad14b-d65e-d943-aa6c-73cd5de52830%2F%29", "section_group": "Vector DBs"}}, {"text": "ription'].values)\n \n# Add data to Chroma DB\nfor idx, embedding in enumerate(embeddings):\n    collection.add(\n        ids=[str(idx)],\n        documents=[data['Description'].iloc[idx]],\n        embeddings=[embedding.tolist()]\n    )\n \n# Query the collection\nquery = \"Sample query text\"\nquery_embedding = model.encode([query])\nresults = collection.query(query_embeddings=[query_embedding.tolist()], n_results=3)\n \nprint(results)\n\nNotes:\n\nFile paths must use backslashes (\\) on Windows and forward slashes (/) on macOS\nThe code is platform-agnostic beyond file paths\n\n\nTroubleshooting Common Issues\nHere are common issues and solutions:\n\nPermission Errors: Ensure write access to the database directory \n\nWindows: Run Command Prompt as administrator\nmacOS: Use sudo if needed\n\n\nDependency Conflicts: Always use a virtual environment to isolate dependencies\nPython Version Issues: Verify Python 3.9+ is installed, as earlier versions won't work with Chroma DB\n\n\nAdditional Resources\nFor further reading and", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🔧 Setup and Configuration", "chunk_index": 5, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cc51ad14b-d65e-d943-aa6c-73cd5de52830%2F%29", "section_group": "Vector DBs"}}, {"text": "s use a virtual environment to isolate dependencies\nPython Version Issues: Verify Python 3.9+ is installed, as earlier versions won't work with Chroma DB\n\n\nAdditional Resources\nFor further reading and advanced configurations, refer to:\n\nChroma DB Documentation\nChroma DB GitHub Repository\n\n\nReferences\n\nChroma DB Installation Guide\nChroma DB Configuration Options\nChroma DB PyPI Page\nChroma DB Official Documentation\nChroma DB GitHub Repository", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🔧 Setup and Configuration", "chunk_index": 6, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7Cc51ad14b-d65e-d943-aa6c-73cd5de52830%2F%29", "section_group": "Vector DBs"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n￼🌈 What is Chroma?\n\nChroma is an open-source vector database that’s built to help you store and search through large amounts of text data using embeddings. That might sound a little techy at first, so let’s break it down. In simple terms, Chroma helps computers understand the meaning behind words and find similar or related ideas—even if the exact words aren’t the same.\n\nImagine you have a big pile of documents or notes. Instead of searching them with just exact keywords (like traditional search), Chroma lets you search by meaning—like asking “What documents are related to cats?” and it finds stuff even if the word “cat” isn’t directly mentioned.\n\n\n🧠 How does it work?\n\nChroma works by using embeddings, which are basically fancy number versions of words or sentences. These embeddings capture the semantic meaning of text (how it relates to other words and ideas). When you store a document in Chroma, it converts the text into vectors—like coordinates in space. Then, whe", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%93%96%20Overview%7C247f1a42-04d8-6c46-8ef4-9662e71e049b%2F%29", "section_group": "Vector DBs"}}, {"text": "e embeddings capture the semantic meaning of text (how it relates to other words and ideas). When you store a document in Chroma, it converts the text into vectors—like coordinates in space. Then, when you want to search or compare, Chroma can quickly find the closest matches based on their positions in this “meaning space.”\n\nIt’s kinda like GPS, but for thoughts instead of places. 🧭✨\n\n\n🛠️ Why would you use Chroma?\n\nChroma is super useful when you’re building apps that need smart search, chatbots with memory, document Q&A systems, or anything where you want to match ideas rather than just keywords. It’s designed to be easy to use with popular tools like LangChain or LLMs (like me!), and it’s fast, flexible, and supports in-memory and persistent storage.\n\n\n\n🚀 TL;DR\n\n\nChroma is a vector database for semantic search.\nIt turns text into embeddings (vectors) so you can search by meaning.\nGreat for AI, chatbots, and intelligent document systems.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📖 Overview", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%93%96%20Overview%7C247f1a42-04d8-6c46-8ef4-9662e71e049b%2F%29", "section_group": "Vector DBs"}}, {"text": "base for semantic search.\nIt turns text into embeddings (vectors) so you can search by meaning.\nGreat for AI, chatbots, and intelligent document systems.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📖 Overview", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FChroma.one%7C3503b8f1-67d1-1645-ae1e-cfca0aed5580%2F%F0%9F%93%96%20Overview%7C247f1a42-04d8-6c46-8ef4-9662e71e049b%2F%29", "section_group": "Vector DBs"}}, {"text": "📖 Overview\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n🌲 What is Pinecone?\n\nPinecone is a vector database built for high-performance similarity search. That means it helps you find things that are “close in meaning”—not just by matching exact words, but by understanding how ideas relate. It’s used a lot in AI and machine learning, especially when working with embeddings from models like OpenAI’s or Sentence Transformers.\n\nSo, if you’ve got a bunch of documents, product descriptions, or even user profiles, Pinecone helps you search through them in a smart, semantic way—like asking, “Which documents are similar to this one?” or “Which products are like this but cheaper?”\n\n\n\n⚙️ How does it work?\n\nPinecone works by storing vectors, which are just numerical representations of things like text, images, or data points. When you convert something like a sentence into a vector using an embedding model, Pinecone can index it and then quickly compare it with others to find the closest matches.\n\nBehind the scenes, it uses some", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Pinecone DB", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FPinecone%20DB.one%7Caade5d8b-ebf8-d34f-a14d-794f087400cf%2F%F0%9F%93%96%20Overview%7C05a77285-6ab2-3a4e-a256-336bc9ba43d9%2F%29", "section_group": "Vector DBs"}}, {"text": "ou convert something like a sentence into a vector using an embedding model, Pinecone can index it and then quickly compare it with others to find the closest matches.\n\nBehind the scenes, it uses some advanced indexing methods to make all this fast, scalable, and accurate—even with millions of entries. It also handles things like versioning, updates, and distributed storage, so you don’t have to worry about building that infrastructure yourself.\n\n\n\n🧰 Why do people use Pinecone?\n\nBecause it saves a ton of time and effort! 😄 Instead of building your own system to store and search embeddings, Pinecone gives you a reliable, production-ready service. It’s especially helpful for:\n\n\nChatbots with memory (storing past conversations)\nPersonalized recommendations\nSemantic document search\nAI-powered search engines\n\n\nYou just plug in your vectors, and Pinecone takes care of the hard part.\n\n\n\n🧠 TL;DR\n\n\nPinecone is a hosted vector database for searching and comparing data based on meaning.\nPerfect f", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Pinecone DB", "page": "📖 Overview", "chunk_index": 1, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FPinecone%20DB.one%7Caade5d8b-ebf8-d34f-a14d-794f087400cf%2F%F0%9F%93%96%20Overview%7C05a77285-6ab2-3a4e-a256-336bc9ba43d9%2F%29", "section_group": "Vector DBs"}}, {"text": "search engines\n\n\nYou just plug in your vectors, and Pinecone takes care of the hard part.\n\n\n\n🧠 TL;DR\n\n\nPinecone is a hosted vector database for searching and comparing data based on meaning.\nPerfect for AI and NLP projects.\nSuper fast, scalable, and easy to integrate with modern machine learning tools.", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Pinecone DB", "page": "📖 Overview", "chunk_index": 2, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FPinecone%20DB.one%7Caade5d8b-ebf8-d34f-a14d-794f087400cf%2F%F0%9F%93%96%20Overview%7C05a77285-6ab2-3a4e-a256-336bc9ba43d9%2F%29", "section_group": "Vector DBs"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Pinecone DB", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FPinecone%20DB.one%7Caade5d8b-ebf8-d34f-a14d-794f087400cf%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C358049a7-04e2-c44c-a1fc-f7f205256887%2F%29", "section_group": "Vector DBs"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Pinecone DB", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FPinecone%20DB.one%7Caade5d8b-ebf8-d34f-a14d-794f087400cf%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7C9e61149b-1162-f644-b3b0-7f93bee5bd3a%2F%29", "section_group": "Vector DBs"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Pinecone DB", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FPinecone%20DB.one%7Caade5d8b-ebf8-d34f-a14d-794f087400cf%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C5d4ffdf7-ed50-4c4e-a9f5-94c50a5c362e%2F%29", "section_group": "Vector DBs"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Pinecone DB", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FPinecone%20DB.one%7Caade5d8b-ebf8-d34f-a14d-794f087400cf%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cea8e7713-171a-4544-a826-2f7239bcd9b3%2F%29", "section_group": "Vector DBs"}}, {"text": "📖 Overview\n\n\n\n\nWeaviate is an open-source vector database designed for semantic search and AI applications. It stores both data objects (like text or images) and their vector embeddings, enabling fast, context-aware searches. Weaviate supports various media types and integrates with machine learning models, making it suitable for tasks like recommendation systems, Q&A, and image similarity search", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Weaviate", "page": "📖 Overview", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FWeaviate.one%7C2f5d28ae-3b8f-40a5-874b-32c0e5c48f3c%2F%F0%9F%93%96%20Overview%7C9b799629-aa02-47ba-88d4-073eaaf19966%2F%29", "section_group": "Vector DBs"}}, {"text": "📜 Known Issues and FAQ's", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Weaviate", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FWeaviate.one%7C2f5d28ae-3b8f-40a5-874b-32c0e5c48f3c%2F%F0%9F%93%9C%20Known%20Issues%20and%20FAQ%27s%7Cb565c40c-3997-4772-83b2-07699042872e%2F%29", "section_group": "Vector DBs"}}, {"text": "💡 Tips Tricks and Shortcuts", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Weaviate", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FWeaviate.one%7C2f5d28ae-3b8f-40a5-874b-32c0e5c48f3c%2F%F0%9F%92%A1%20Tips%20Tricks%20and%20Shortcuts%7C82c118fa-717a-413c-b026-5676c5f64c29%2F%29", "section_group": "Vector DBs"}}, {"text": "🧩 Use Cases and Example Code", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Weaviate", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FWeaviate.one%7C2f5d28ae-3b8f-40a5-874b-32c0e5c48f3c%2F%F0%9F%A7%A9%20Use%20Cases%20and%20Example%20Code%7Cea614163-2b04-48d3-b359-60e8f81ad339%2F%29", "section_group": "Vector DBs"}}, {"text": "🔧 Setup and Configuration", "metadata": {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Weaviate", "page": "🔧 Setup and Configuration", "chunk_index": 0, "page_url": "https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/SiteAssets/Creospan%20Knowledge%20Exchange%20Notebook?wd=target%28Vector%20DBs%2FWeaviate.one%7C2f5d28ae-3b8f-40a5-874b-32c0e5c48f3c%2F%F0%9F%94%A7%20Setup%20and%20Configuration%7C896d45e3-2cc9-43fe-a2d7-577b79d04d51%2F%29", "section_group": "Vector DBs"}}]