from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from openai import OpenAI
import faiss
import numpy as np
import json
import os
from mangum import Mangum  # ✅ AWS Lambda adapter

# === CONFIG ===
EMBEDDING_MODEL = "text-embedding-3-small"
INDEX_FILE = "faiss.index"
METADATA_FILE = "faiss_metadata.json"
CHUNKS_FILE = "onenote_chunks.json"
# New config for second index
NEW_INDEX_FILE = "faiss_new.index"
NEW_METADATA_FILE = "faiss_new_metadata.json"
NEW_CHUNKS_FILE = "onenote_new_chunks.json"

# Add a function to create a new FAISS index
def create_new_faiss_index(chunks_data, output_index_file, output_metadata_file):
    """
    Create a new FAISS index from chunks data
    
    Args:
        chunks_data: List of text chunks with metadata
        output_index_file: Path to save the FAISS index
        output_metadata_file: Path to save the metadata
    """
    # Extract text from chunks
    texts = [chunk.get("text", "") for chunk in chunks_data]
    
    # Create embeddings
    embeddings = []
    metadata = []
    
    print(f"Generating embeddings for {len(texts)} chunks...")
    
    # Process in batches to avoid rate limits
    batch_size = 100
    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i+batch_size]
        batch_chunks = chunks_data[i:i+batch_size]
        
        # Generate embeddings
        response = client.embeddings.create(
            input=batch_texts,
            model=EMBEDDING_MODEL
        )
        
        # Extract embeddings and metadata
        batch_embeddings = [item.embedding for item in response.data]
        batch_metadata = [chunk.get("metadata", {}) for chunk in batch_chunks]
        
        embeddings.extend(batch_embeddings)
        metadata.extend(batch_metadata)
        
        print(f"Processed {i+len(batch_texts)}/{len(texts)} chunks")
    
    # Convert to numpy array
    embeddings_array = np.array(embeddings, dtype="float32")
    
    # Create FAISS index
    dimension = len(embeddings_array[0])
    new_index = faiss.IndexFlatL2(dimension)
    
    # Add vectors to index
    new_index.add(embeddings_array)
    
    # Save index and metadata
    faiss.write_index(new_index, output_index_file)
    
    with open(output_metadata_file, "w", encoding="utf-8") as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)
    
    print(f"Created new FAISS index with {len(embeddings)} vectors")
    return new_index, metadata

# === REQUEST BODY MODEL ===
class SearchRequest(BaseModel):
    query: str
    top_k: int = 5
    generate_answer: bool = True

# === INIT OPENAI & LOAD DATA ===
api_key = os.getenv("OPENAI_API_KEY")
if not api_key:
    raise RuntimeError("OPENAI_API_KEY is not set in environment")

client = OpenAI(api_key=api_key)

try:
    index = faiss.read_index(INDEX_FILE)
except Exception as e:
    raise RuntimeError(f"Failed to load FAISS index: {e}")

try:
    with open(METADATA_FILE, "r", encoding="utf-8") as f:
        metadata = json.load(f)
except Exception as e:
    raise RuntimeError(f"Failed to load metadata: {e}")

try:
    with open(CHUNKS_FILE, "r", encoding="utf-8") as f:
        full_chunks = json.load(f)
except Exception as e:
    raise RuntimeError(f"Failed to load chunks: {e}")

# === FASTAPI SETUP ===
app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {"status": "ok"}

@app.post("/search")
def search(req: SearchRequest):
    response = client.embeddings.create(
        input=req.query,
        model=EMBEDDING_MODEL
    )
    query_vector = np.array([response.data[0].embedding], dtype="float32")

    D, I = index.search(query_vector, req.top_k)

    results = []
    context_chunks = []

    for idx in I[0]:
        if idx < len(full_chunks):
            chunk = full_chunks[idx]
            meta = chunk.get("metadata", {})
            result = {
                "notebook": meta.get("notebook", "Unknown Notebook"),
                "section": meta.get("section", "Unknown Section"),
                "page": meta.get("page", "Unknown Page"),
                "page_id": meta.get("page_id"),  # Optional internal use
                "page_url": meta.get("page_url"),
                "chunk_index": meta.get("chunk_index", idx),
                "source_path": f"{meta.get('notebook', '?')} / {meta.get('section', '?')} / {meta.get('page', '?')}",
                "text": chunk.get("text", ""),
                "text_snippet": chunk.get("text", "")[:200] + "…"  # Optional quick view
            }
            context_chunks.append(result["text"])
            results.append(result)

    final_answer = None
    if req.generate_answer:
        joined_context = "\n\n".join(context_chunks)
        chat = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {
                    "role": "system",
                    "content": "You are a helpful assistant. Only use the provided context to answer. Do not add external knowledge."
                },
                {
                    "role": "user",
                    "content": f"Context:\n\n{joined_context}\n\nAnswer this question:\n{req.query}"
                }
            ]
        )
        final_answer = chat.choices[0].message.content

    return {
        "query": req.query,
        "results": results,
        "answer": final_answer
    }

@app.post("/create-new-index")
def create_index(chunks_file: str = NEW_CHUNKS_FILE):
    """Create a new FAISS index from chunks file"""
    try:
        # Load chunks data
        with open(chunks_file, "r", encoding="utf-8") as f:
            chunks_data = json.load(f)
        
        # Create new index
        new_index, new_metadata = create_new_faiss_index(
            chunks_data, 
            NEW_INDEX_FILE, 
            NEW_METADATA_FILE
        )
        
        return {
            "status": "success",
            "message": f"Created new FAISS index with {new_index.ntotal} vectors",
            "index_file": NEW_INDEX_FILE,
            "metadata_file": NEW_METADATA_FILE
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e)
        }

@app.post("/search-new-index")
def search_new_index(req: SearchRequest):
    """Search the new FAISS index"""
    try:
        # Load new index and metadata
        new_index = faiss.read_index(NEW_INDEX_FILE)
        
        with open(NEW_METADATA_FILE, "r", encoding="utf-8") as f:
            new_metadata = json.load(f)
            
        with open(NEW_CHUNKS_FILE, "r", encoding="utf-8") as f:
            new_chunks = json.load(f)
        
        # Generate embedding for query
        response = client.embeddings.create(
            input=req.query,
            model=EMBEDDING_MODEL
        )
        query_vector = np.array([response.data[0].embedding], dtype="float32")
        
        # Search
        D, I = new_index.search(query_vector, req.top_k)
        
        # Process results
        results = []
        context_chunks = []
        
        for idx in I[0]:
            if idx < len(new_chunks):
                chunk = new_chunks[idx]
                meta = chunk.get("metadata", {})
                result = {
                    "notebook": meta.get("notebook", "Unknown Notebook"),
                    "section": meta.get("section", "Unknown Section"),
                    "page": meta.get("page", "Unknown Page"),
                    "page_id": meta.get("page_id"),
                    "page_url": meta.get("page_url"),
                    "chunk_index": meta.get("chunk_index", idx),
                    "source_path": f"{meta.get('notebook', '?')} / {meta.get('section', '?')} / {meta.get('page', '?')}",
                    "text": chunk.get("text", ""),
                    "text_snippet": chunk.get("text", "")[:200] + "…"
                }
                context_chunks.append(result["text"])
                results.append(result)
        
        # Generate answer if requested
        final_answer = None
        if req.generate_answer:
            joined_context = "\n\n".join(context_chunks)
            chat = client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a helpful assistant. Only use the provided context to answer. Do not add external knowledge."
                    },
                    {
                        "role": "user",
                        "content": f"Context:\n\n{joined_context}\n\nAnswer this question:\n{req.query}"
                    }
                ]
            )
            final_answer = chat.choices[0].message.content
        
        return {
            "query": req.query,
            "results": results,
            "answer": final_answer
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e)
        }

# ✅ Lambda entrypoint
handler = Mangum(app)

# Optional: local run
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)
