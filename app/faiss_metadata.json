[{"notebook": "Creospan Knowledge Exchange Notebook", "section": "AI-assisted Functional Testing", "page": "📖 Overview", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AI-assisted Functional Testing", "page": "🔧 Setup and Configuration", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AI-assisted Functional Testing", "page": "🧩 Use Cases and Example Code", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AI-assisted Functional Testing", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AI-assisted Functional Testing", "page": "📜 Known Issues and FAQ's", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AI-assisted Functional Testing", "page": "Test Sigma", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AI-assisted Functional Testing", "page": "Strategy", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AWS lambda docker brickwork with next.js", "page": "🔧 Setup and Configuration", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AWS lambda docker brickwork with next.js", "page": "📖 Overview", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AWS lambda docker brickwork with next.js", "page": "📜 Known Issues and FAQ's", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AWS lambda docker brickwork with next.js", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "AWS lambda docker brickwork with next.js", "page": "🧩 Use Cases and Example Code", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "cursor.ai", "page": "📜 Known Issues and FAQ's", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "cursor.ai", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "cursor.ai", "page": "🧩 Use Cases and Example Code", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "cursor.ai", "page": "🔧 Setup and Configuration", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "cursor.ai", "page": "📖 Overview", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "FAISS", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "FAISS", "page": "🧩 Use Cases and Example Code", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "FAISS", "page": "📜 Known Issues and FAQ's", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "FAISS", "page": "🔧 Setup and Configuration", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "FAISS", "page": "📖 Overview", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "General", "page": "Start flow here - Checklist", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "📖 Overview", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "📖 Overview", "chunk_index": 1}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "📜 Known Issues and FAQ's", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "📜 Known Issues and FAQ's", "chunk_index": 1}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🔧 Setup and Configuration", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🔧 Setup and Configuration", "chunk_index": 1}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🔧 Setup and Configuration", "chunk_index": 2}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🔧 Setup and Configuration", "chunk_index": 3}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 1}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 2}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 3}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🧩 Use Cases and Example Code", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🧩 Use Cases and Example Code", "chunk_index": 1}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🧩 Use Cases and Example Code", "chunk_index": 2}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🧩 Use Cases and Example Code", "chunk_index": 3}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🧩 Use Cases and Example Code", "chunk_index": 4}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "n8n - Low Code-No Code tool", "page": "🧩 Use Cases and Example Code", "chunk_index": 5}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "OneNote export to FAISS", "page": "📜 Known Issues and FAQ's", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "OneNote export to FAISS", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "OneNote export to FAISS", "page": "🧩 Use Cases and Example Code", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "OneNote export to FAISS", "page": "🔧 Setup and Configuration", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "OneNote export to FAISS", "page": "📖 Overview", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG on AWS", "page": "📖 Overview", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG on AWS", "page": "🔧 Setup and Configuration", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG on AWS", "page": "🧩 Use Cases and Example Code", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG on AWS", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG on AWS", "page": "📜 Known Issues and FAQ's", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "READ ME FIRST", "page": "Read Me – How to use and contribute to this notebook", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "READ ME FIRST", "page": "Read Me – How to use and contribute to this notebook", "chunk_index": 1}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "READ ME FIRST", "page": "Read Me – How to use and contribute to this notebook", "chunk_index": 2}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "📖 Overview", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "🔧 Setup and Configuration", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "🧩 Use Cases and Example Code", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "📜 Known Issues and FAQ's", "chunk_index": 0}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 1, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 2, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 3, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 4, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 5, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 6, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 7, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 8, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 9, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🧩 Use Cases and Example Code", "chunk_index": 10, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "📖 Overview", "chunk_index": 0, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "📖 Overview", "chunk_index": 1, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "📖 Overview", "chunk_index": 2, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🔧 Setup and Configuration", "chunk_index": 1, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🔧 Setup and Configuration", "chunk_index": 2, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "🔧 Setup and Configuration", "chunk_index": 3, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Agents", "page": "OpenAi Agents SDK", "chunk_index": 0, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "📖 Overview – Model Context Protocol", "chunk_index": 0, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "📖 Overview – Model Context Protocol", "chunk_index": 1, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "📖 Overview – Model Context Protocol", "chunk_index": 2, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "📜 Known Issues and FAQs", "chunk_index": 0, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "🧩 Use Cases and Example Code", "chunk_index": 1, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "🧩 Use Cases and Example Code", "chunk_index": 2, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "MCP", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Agentic AI"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CardRandomizer_nextjs_port", "page": "📖 Overview", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CardRandomizer_nextjs_port", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CardRandomizer_nextjs_port", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CardRandomizer_nextjs_port", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CardRandomizer_nextjs_port", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Full Stack Invoice app", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Full Stack Invoice app", "page": "🧩 Use Cases and Example Code", "chunk_index": 1, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Full Stack Invoice app", "page": "🧩 Use Cases and Example Code", "chunk_index": 2, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Full Stack Invoice app", "page": "📖 Overview", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Full Stack Invoice app", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Full Stack Invoice app", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 1, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 2, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 3, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 4, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 5, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 6, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 7, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Reverse Engineering: [Codebase --> Use-cases --> UI]", "chunk_index": 8, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "CI/CD Pipeline", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "SendGrid Configuration", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "SendGrid Configuration", "chunk_index": 1, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "SendGrid Configuration", "chunk_index": 2, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "SendGrid Configuration", "chunk_index": 3, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "🔧 Setup and Configuration", "chunk_index": 1, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "🔧 Setup and Configuration", "chunk_index": 2, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "Upstash Configuration [Soon to be replaced with GCP services] ", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "📖 Overview", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "📖 Overview", "chunk_index": 1, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Health Plans App", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RFP Navigator", "page": "📖 Overview", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RFP Navigator", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RFP Navigator", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RFP Navigator", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RFP Navigator", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "Applications"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "<PERSON><PERSON><PERSON>", "chunk_index": 0, "section_group": "Gen AI Prompts"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "<PERSON><PERSON><PERSON>", "chunk_index": 1, "section_group": "Gen AI Prompts"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "<PERSON><PERSON><PERSON>", "chunk_index": 2, "section_group": "Gen AI Prompts"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "<PERSON><PERSON><PERSON>", "chunk_index": 3, "section_group": "Gen AI Prompts"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "<PERSON><PERSON><PERSON>", "chunk_index": 4, "section_group": "Gen AI Prompts"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "📖 Overview", "chunk_index": 0, "section_group": "Gen AI Prompts"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Gen AI Prompts"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Gen AI Prompts"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Gen AI Prompts"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "📜 Known Issues and FAQs", "chunk_index": 0, "section_group": "Gen AI Prompts"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gen AI Prompts", "page": "Full AI Prompt Database - <PERSON>", "chunk_index": 0, "section_group": "Gen AI Prompts"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Abacus.ai", "page": "📖 Overview", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Abacus.ai", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Abacus.ai", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Abacus.ai", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Abacus.ai", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "ChatGPT", "page": "📖 Overview", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "ChatGPT", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "ChatGPT", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "ChatGPT", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "ChatGPT", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Notebook LLM", "page": "📖 Overview", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Notebook LLM", "page": "📖 Overview", "chunk_index": 1, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Notebook LLM", "page": "📖 Overview", "chunk_index": 2, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Notebook LLM", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Notebook LLM", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Notebook LLM", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Notebook LLM", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Open-WebUi", "page": "📖 Overview", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Open-WebUi", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Open-WebUi", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Open-WebUi", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Open-WebUi", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "Gen AI Tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CodeSandBox.io", "page": "📖 Overview", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CodeSandBox.io", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CodeSandBox.io", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CodeSandBox.io", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "CodeSandBox.io", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Cursor.ai", "page": "📖 Overview", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Cursor.ai", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Cursor.ai", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Cursor.ai", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Cursor.ai", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Replit.com", "page": "📖 Overview", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Replit.com", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Replit.com", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Replit.com", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Replit.com", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "📖 Overview", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "📖 Overview", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Visual Studio Code", "page": "📖 Overview", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Visual Studio Code", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Visual Studio Code", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Visual Studio Code", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Visual Studio Code", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "IDEs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Claude 3.7", "page": "📖 Overview", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Claude 3.7", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Claude 3.7", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Claude 3.7", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Claude 3.7", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gemini 2.5", "page": "📖 Overview", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gemini 2.5", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gemini 2.5", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gemini 2.5", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Gemini 2.5", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "📖 Overview", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "📖 Overview", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "LLama 4", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "📜 Known Issues and FAQ's", "chunk_index": 1, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "📜 Known Issues and FAQ's", "chunk_index": 2, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 1, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🧩 Use Cases and Example Code", "chunk_index": 1, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🧩 Use Cases and Example Code", "chunk_index": 2, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🧩 Use Cases and Example Code", "chunk_index": 3, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🧩 Use Cases and Example Code", "chunk_index": 4, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🔧 Setup and Configuration", "chunk_index": 1, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🔧 Setup and Configuration", "chunk_index": 2, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "🔧 Setup and Configuration", "chunk_index": 3, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "📖 Overview", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "📖 Overview", "chunk_index": 1, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "📖 Overview", "chunk_index": 2, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Ollama", "page": "📖 Overview", "chunk_index": 3, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "📖 Overview", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "📖 Overview", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Perplexity", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "LLM Models"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Automate Selenium tests using ai", "page": "📖 Overview", "chunk_index": 0, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Automate Selenium tests using ai", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Automate Selenium tests using ai", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Automate Selenium tests using ai", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Automate Selenium tests using ai", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "📖 Overview", "chunk_index": 0, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "📖 Overview", "chunk_index": 1, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "📖 Overview", "chunk_index": 2, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "📜 Known Issues and FAQ's", "chunk_index": 1, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "📜 Known Issues and FAQ's", "chunk_index": 2, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "🧩 Use Cases and Example Code", "chunk_index": 1, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "🧩 Use Cases and Example Code", "chunk_index": 2, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 1, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 2, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "🔧 Setup and Configuration", "chunk_index": 1, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "From figma export create react site", "page": "🔧 Setup and Configuration", "chunk_index": 2, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG-PythonExamples", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG-PythonExamples", "page": "📖 Overview", "chunk_index": 0, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG-PythonExamples", "page": "📖 Overview", "chunk_index": 1, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG-PythonExamples", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG-PythonExamples", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "RAG-PythonExamples", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "POCs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "GitHub CoPilot", "page": "📖 Overview", "chunk_index": 0, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "GitHub CoPilot", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "GitHub CoPilot", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "GitHub CoPilot", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "GitHub CoPilot", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "GitHub CoPilot", "page": "GitHub Copilot for user stories", "chunk_index": 0, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "GitHub CoPilot", "page": "GitHub Copilot for user stories", "chunk_index": 1, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "📖 Overview", "chunk_index": 0, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "More overview", "chunk_index": 0, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "More overview", "chunk_index": 1, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "More overview", "chunk_index": 2, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "More overview", "chunk_index": 3, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "More overview", "chunk_index": 4, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 0, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 1, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 2, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 3, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 4, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 5, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 6, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Overview", "page": "Leading PM tools as of 4/22/25", "chunk_index": 7, "section_group": "Product Manager <PERSON>ls"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Template <PERSON>s", "page": "📖 Overview", "chunk_index": 0, "section_group": "Templates"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Template <PERSON>s", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Templates"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Template <PERSON>s", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Templates"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Template <PERSON>s", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Templates"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Template <PERSON>s", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "Templates"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "📖 Overview", "chunk_index": 0, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "UX Pilot vs V.0", "chunk_index": 0, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "UX Pilot vs V.0", "chunk_index": 1, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "UX Pilot vs V.0", "chunk_index": 2, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "UX Pilot vs V.0", "chunk_index": 3, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "UX Pilot vs V.0", "chunk_index": 4, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "UX Pilot vs V.0", "chunk_index": 5, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "UX Pilot", "page": "UX Pilot", "chunk_index": 0, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "📜 Known Issues and FAQ's", "chunk_index": 1, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "📜 Known Issues and FAQ's", "chunk_index": 2, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 1, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "🔧 Setup and Configuration", "chunk_index": 1, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "🔧 Setup and Configuration", "chunk_index": 2, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "📖 Overview", "chunk_index": 0, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "📖 Overview", "chunk_index": 1, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "📖 Overview", "chunk_index": 2, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "📖 Overview", "chunk_index": 3, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "V.0 (in IDE as well)", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "UX tools"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📜 Known Issues and FAQ's", "chunk_index": 1, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📜 Known Issues and FAQ's", "chunk_index": 2, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📜 Known Issues and FAQ's", "chunk_index": 3, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📜 Known Issues and FAQ's", "chunk_index": 4, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 1, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 2, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 3, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 4, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 5, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 6, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 7, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 8, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 9, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 10, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 11, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 12, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 13, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 14, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 15, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 16, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 17, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 18, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 19, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 20, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 21, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 22, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 23, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 24, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 1, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 2, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 3, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 4, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 5, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 6, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 7, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 8, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 9, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 10, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 11, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 12, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 13, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 14, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 15, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 16, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 17, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 18, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 19, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 20, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🧩 Use Cases and Example Code", "chunk_index": 21, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🔧 Setup and Configuration", "chunk_index": 1, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🔧 Setup and Configuration", "chunk_index": 2, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🔧 Setup and Configuration", "chunk_index": 3, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🔧 Setup and Configuration", "chunk_index": 4, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🔧 Setup and Configuration", "chunk_index": 5, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "🔧 Setup and Configuration", "chunk_index": 6, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📖 Overview", "chunk_index": 0, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📖 Overview", "chunk_index": 1, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Chroma", "page": "📖 Overview", "chunk_index": 2, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Pinecone DB", "page": "📖 Overview", "chunk_index": 0, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Pinecone DB", "page": "📖 Overview", "chunk_index": 1, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Pinecone DB", "page": "📖 Overview", "chunk_index": 2, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Pinecone DB", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Pinecone DB", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Pinecone DB", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Pinecone DB", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Weaviate", "page": "📖 Overview", "chunk_index": 0, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Weaviate", "page": "📜 Known Issues and FAQ's", "chunk_index": 0, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Weaviate", "page": "💡 Tips Tricks and Shortcuts", "chunk_index": 0, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Weaviate", "page": "🧩 Use Cases and Example Code", "chunk_index": 0, "section_group": "Vector DBs"}, {"notebook": "Creospan Knowledge Exchange Notebook", "section": "Weaviate", "page": "🔧 Setup and Configuration", "chunk_index": 0, "section_group": "Vector DBs"}]