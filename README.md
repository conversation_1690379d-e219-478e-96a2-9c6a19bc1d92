
# Welcome to your CDK TypeScript project

This is a blank project for CDK development with TypeScript.

The `cdk.json` file tells the CDK Toolkit how to execute your app.

## Useful commands

* `npm run build`   compile typescript to js
* `npm run watch`   watch for changes and compile
* `npm run test`    perform the jest unit tests
* `npx cdk deploy`  deploy this stack to your default AWS account/region
* `npx cdk diff`    compare deployed stack with current state
* `npx cdk synth`   emits the synthesized CloudFormation template
=======
# 🔍 ke_faiss_api: FastAPI + FAISS + OpenAI RAG Chatbot (AWS Lambda Deploy)

This project deploys a serverless Retrieval-Augmented Generation (RAG) API using FastAPI, FAISS, and OpenAI—served via AWS Lambda using Docker and AWS CDK. It supports querying semantically embedded content (e.g., OneNote export) through a `/search` endpoint.

---

## ✅ Features

- 🔎 FAISS-powered vector search
- 💡 OpenAI embedding + GPT-4 generation
- 🚀 FastAPI backend via AWS Lambda (with Mangum)
- 📦 Dockerized Lambda build
- 📡 Public Function URL (or deployable via API Gateway)
- 🔒 Environment secrets via `.env`

---

## 📁 Project Structure

```
ke_faiss_api/
├── app/
│   ├── main.py                # FastAPI + Mangum Lambda app
│   ├── faiss.index            # Vector index
│   ├── faiss_metadata.json    # Metadata per chunk
│   ├── onenote_chunks.json    # Chunked text data
│   └── requirements.txt       # Python requirements
├── Dockerfile                 # Lambda-compatible image
cdk_ke_faiss_api/
├── lib/lambda_stack.ts       # CDK stack for DockerImageFunction
└── bin/cdk.ts                 # CDK entrypoint
```

---

## 🧱 Prerequisites

- Python 3.11
- Node.js / npm
- AWS CLI + credentials
- AWS CDK: `npm install -g aws-cdk`
- Docker (must support `linux/amd64`)
- OpenAI API key

---

## 🔨 1. Build Your FAISS Embeddings

Export your content (e.g., from OneNote), then use a local script to:

- Chunk and embed using `text-embedding-3-small`
- Save:
  - `faiss.index`
  - `faiss_metadata.json`
  - `onenote_chunks.json`

Place these in `ke_faiss_api/app/`.

---

## 🐳 2. Dockerfile (Lambda-Compatible)

```dockerfile
FROM public.ecr.aws/lambda/python:3.11
RUN yum install -y gcc gcc-c++ make swig wget git && yum clean all
COPY requirements.txt ./
RUN pip install --upgrade pip && pip install -r requirements.txt
COPY app/ ${LAMBDA_TASK_ROOT}/
CMD ["main.handler"]
```

🧪 Optional: Run locally:

```bash
docker build -t ke-faiss-api .
docker run --rm -it -e OPENAI_API_KEY=sk-... ke-faiss-api main.handler
```

---

## ☁️ 3. CDK Stack

File: `cdk_ke_faiss_api/lib/lambda_stack.ts`

```ts
const lambdaFn = new lambda.DockerImageFunction(this, "FaissApiLambda", {
  code: lambda.DockerImageCode.fromImageAsset("../ke_faiss_api", {
    platform: "linux/amd64",
  }),
  memorySize: 3008,
  timeout: cdk.Duration.seconds(30),
  environment: {
    OPENAI_API_KEY: process.env.OPENAI_API_KEY || "",
  },
});

const fnUrl = lambdaFn.addFunctionUrl({ authType: lambda.FunctionUrlAuthType.NONE });

new cdk.CfnOutput(this, "FunctionUrl", {
  value: fnUrl.url,
});
```

Run:

```bash
cd cdk_ke_faiss_api
npm install
cdk deploy
```

---

## 🌐 4. Call Your Lambda

```bash
curl -X POST "<your-lambda-url>/search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "give me an overview of n8n"
  }' | jq .
```

✅ You’ll receive:
- Top `k` chunks
- Original notebook/page/section
- GPT-4 answer (if enabled)

---

## 🔐 Environment Setup

Create a `.env`:

```
OPENAI_API_KEY=sk-...
```

CDK and Docker will use this for secure builds.

---

## 📓 Notes

- Handles CORS automatically via FastAPI middleware
- You can later plug this into an API Gateway with routes like `/search`, `/health`, etc.
- Optimized for RAG queries over enterprise notebooks or PDFs

---

## 🛠️ Troubleshooting

- ❌ `Runtime.InvalidEntrypoint`: Ensure `CMD ["main.handler"]` and file is copied to `${LAMBDA_TASK_ROOT}`
- ❌ 401 Unauthorized: Use `.env` or CDK `environment` block to inject OpenAI key
- ❌ CORS error: ensure FunctionUrl or API Gateway has CORS enabled

---

