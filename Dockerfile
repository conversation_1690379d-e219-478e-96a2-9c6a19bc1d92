FROM public.ecr.aws/lambda/python:3.11

# Install system packages required for faiss
RUN yum install -y \
    gcc gcc-c++ make swig wget git \
    && yum clean all

# Set environment variable (injected at runtime in production)
ENV PYTHONUNBUFFERED=1

# Install Python dependencies
COPY requirements.txt ./
RUN pip install --upgrade pip && pip install -r requirements.txt

# Copy app code and data
COPY app/ ${LAMBDA_TASK_ROOT}/

# AWS Lambda entry point (expects handler = Mangum(app) in main.py)
CMD ["main.handler"]