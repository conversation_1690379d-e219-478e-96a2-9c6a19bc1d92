#!/bin/bash

set -e

echo "🔁 Rebuilding Docker image..."
docker build -t ke-faiss-api .

echo "🔍 Validating contents inside container..."
docker run --rm -it ke-faiss-api sh -c "ls -lh onenote_chunks.json faiss.index faiss_metadata.json || echo '❌ Some files missing'"

echo "🧪 Running local Lambda simulation on port 9000..."
docker run --rm -d \
  -p 9000:8080 \
  -e OPENAI_API_KEY=$OPENAI_API_KEY \
  --name ke-faiss-local \
  ke-faiss-api

echo "⏳ Waiting for Lambda to initialize..."
sleep 3

echo "📤 Sending test query to /search..."
curl -s -XPOST "http://localhost:9000/2015-03-31/functions/function/invocations" \
  -H "Content-Type: application/json" \
  -d '{"httpMethod":"POST","path":"/search","body":"{\"query\":\"give me a short overview of n8n\"}"}' \
  | jq .

echo "🧼 Stopping local container..."
docker stop ke-faiss-local

echo "🚀 Redeploying to AWS via CDK..."
cd ../cdk_ke_faiss_api
OPENAI_API_KEY=$OPENAI_API_KEY npx cdk deploy