
*.js
!jest.config.js
*.d.ts
node_modules

# CDK asset staging directory
.cdk.staging
cdk.out
=======
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Virtual environment
venv/
env/
ENV/
.venv/
.pipenv/
*.egg-info/

# PyCharm project files
.idea/

# Distribution / packaging
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg
MANIFEST

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
hypothesis/

# Jupyter Notebook checkpoints
.ipynb_checkpoints

# PyInstaller
*.manifest
*.spec

# VS Code (if someone uses it too)
.vscode/


# Mac / Linux system files
.DS_Store
Thumbs.db
